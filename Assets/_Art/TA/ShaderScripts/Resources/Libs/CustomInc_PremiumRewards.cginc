#ifndef CUSTOMINC_PREMIUM_REWARDS
#define CUSTOMINC_PREMIUM_REWARDS

#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_ColourAdjustments.cginc"

sampler2D _GlitterTex;
half _GlitterTile;
sampler2D _IridescenceTex;
sampler2D _ProductAgingTex;
sampler2D _DamageTex;
float _DamageTexTiling;
float4 _UVOffsetAging;


float3 _Glitter(half2 uv, half3 worldNormalVert, float4 tint, half4 premiumVars, half3 viewDir, float3 worldPos, float3x3 tspace)
{
	float4 glitterVars = tex2D(_GlitterTex, uv * _GlitterTile);
	half3 normalFromGlitterCol = normalize((glitterVars.xyz - 0.5) * 2);
	half3 glitterNormal = _WorldNormal(tspace[0], tspace[1], tspace[2], normalFromGlitterCol.xyz);


	half rim = 1 - saturate(dot(viewDir, worldNormalVert));

	half3 normalView = normalize(mul((float3x3)UNITY_MATRIX_V, worldNormalVert));
	half facingDotGlitter = saturate(dot(viewDir, normalize(glitterNormal)));

	facingDotGlitter *= 20;


	facingDotGlitter = sin(facingDotGlitter);
	facingDotGlitter = smoothstep(0.5, 1, facingDotGlitter);
	facingDotGlitter = saturate(facingDotGlitter + saturate(rim - 0.25));


	float3 outputGlitter = facingDotGlitter * saturate(tint.xyz + 0.4);
	return float3(outputGlitter * premiumVars.x);
}


float3 _Glitter_LQ(half2 uv, half3 worldNormalVert, float4 tint, half4 premiumVars, half3 viewDir, float3 worldPos)
{
	float4 glitterVars = tex2D(_GlitterTex, uv * _GlitterTile);
	half3 normalFromGlitterCol = normalize((glitterVars.xyz - 0.5) * 2);
	half3 glitterNormal = normalize(worldNormalVert + normalFromGlitterCol);


	half rim = 1 - saturate(dot(viewDir, worldNormalVert));

	half3 normalView = normalize(mul((float3x3)UNITY_MATRIX_V, worldNormalVert));
	half facingDotGlitter = saturate(dot(viewDir, normalize(glitterNormal)));

	facingDotGlitter *= 20;


	facingDotGlitter = sin(facingDotGlitter);
	facingDotGlitter = smoothstep(0.5, 1, facingDotGlitter);
	facingDotGlitter = saturate(facingDotGlitter + saturate(rim - 0.25));


	float3 outputGlitter = facingDotGlitter * saturate(tint.xyz + 0.4);
	return float3(outputGlitter * premiumVars.x);
}



float3 _Iridescence(half3 viewDir, half3 worldNormal)
{
	half2 matcapUV = normalize(mul((float3x3)UNITY_MATRIX_V, worldNormal)).xy * 0.5 + 0.5;

	float3 iridescence = tex2D(_IridescenceTex, matcapUV);

	return iridescence;
}


float3 Grunge(half2 meshUV, half2 agingUV, float4 tint, half aging, float3 worldPos, float3 currentColour, inout half3 normals)
{
	float2 uv_ddx = ddx(agingUV * .5 * _DamageTexTiling);
	float2 uv_ddy = ddy(agingUV * .5 * _DamageTexTiling);
	agingUV = frac(agingUV * _DamageTexTiling);
	agingUV = agingUV * .5 + _UVOffsetAging.xy;


	float4 grunge = tex2Dgrad(_ProductAgingTex, agingUV, uv_ddx, uv_ddy);

	//aging = _FloatRemapCurve(aging).y;

	half grungeMap = grunge.w - 1;
	grungeMap += aging;
	grungeMap = saturate(grungeMap);

	normals = lerp(normals, -normals, grungeMap * 0.5);

	float3 output = lerp(currentColour, grunge.xyz, grungeMap);


	return output.xyz;
}

#endif // CUSTOMINC_PREMIUM_REWARDS