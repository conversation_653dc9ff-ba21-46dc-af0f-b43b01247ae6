#ifndef CUSTOMINC_FRAG_SHADERS
#define CUSTOMINC_FRAG_SHADERS

#if WORLD_UI
	#if PROGRESS_BAR
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"
		
		fixed4 frag_ProgressBar (v2f_ProgressBar i) : SV_Target
		{
			UNITY_SETUP_INSTANCE_ID(i);
			_PROP_FRAG_DEFINES(i);


			float progress =  UNITY_ACCESS_INSTANCED_PROP(Props, _Progress);
			progress = max(0.001, progress);

			#if _CORNERS_ROUND_ONE
				half slider = _RoundOneEnd(i.uv, progress, _CornerRoundness);
				slider = step(0.1, slider);
			#elif _CORNERS_ROUND_BOTH
				half sliderRoundOne = _RoundOneEnd(i.uv, progress, _CornerRoundness);

				half startShape = _StartShape(i.uv, _CornerRoundness);

				sliderRoundOne *= startShape;
				half slider = step(0.1, sliderRoundOne);
			#elif _CORNERS_SHARP
				half mask = (1-i.uv.x) + 0.1;
				half sliderMask = saturate(mask - progress);

				half slider = 1-step(0.1, sliderMask);
			#endif

			//return mask;
				
			worldNormal = half3(0, 0, 1);
			#if HIGH_SPEC
				_WORLD_NORMAL_FRAG(i);
			#else
				_WORLD_NORMAL_FRAG_LQ(i);
			#endif
			_CACHED_SHADOW_MASKED_FRAG(i);
			_LIGHT_FROM_NORMAL_WORLD(i);
			_APPLY_SHADOW_FRAG(i);
				
			fixed4 progressBarCol = UNITY_ACCESS_INSTANCED_PROP(Props, _ProgressBarCol);
				
			#if HAS_SECOND_PROGRESS
				float secondProgress =  UNITY_ACCESS_INSTANCED_PROP(Props, _SecondProgressBar);
				half secondMask = smoothstep(secondProgress * 0.98, secondProgress ,  1-i.uv.x);
			#endif
			//half mask = step(1-i.uv, progress);
			fixed4 unlitAlbedo;

			half clipValue = -0.1;
				
			#if _BACKGROUND_CLIP
				clip(clipValue + slider);
				unlitAlbedo = progressBarCol;
			#elif _BACKGROUND_SHARP
				unlitAlbedo = lerp(progressBarCol, _BackgroundCol, step(slider, 0.001));
			#elif _BACKGROUND_ROUND
				half bgRoundOne = 1;
				half bgStartShape = 1;
				#if _CORNERS_ROUND_ONE
					bgRoundOne = _RoundOneEnd(i.uv, 1, _CornerRoundness);
				#endif
				#if _CORNERS_ROUND_BOTH
					bgRoundOne = _RoundOneEnd(i.uv, 1, _CornerRoundness);
					bgStartShape = _StartShape(i.uv, _CornerRoundness);
				#endif

				#if _CORNERS_ROUND_ONE | _CORNERS_ROUND_BOTH
					bgRoundOne *= bgStartShape;
					half bgMask = step(0.1, bgRoundOne);

					clip(clipValue + bgMask);
				#endif

				unlitAlbedo = lerp(progressBarCol, _BackgroundCol, step(slider, 0.001));
			#endif

			#if HAS_SECOND_PROGRESS
				unlitAlbedo = lerp(unlitAlbedo, _SecondProgressBarCol, secondMask);
			#endif

			fixed4 output;
			output.xyz = _LitAlbedo(unlitAlbedo.xyz, shadow);
			output.w = 1;

			return output;
		}

	#endif

	#if COUNTER
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_BoxClip.cginc"

		fixed4 frag_Counter(v2f_Counter i) : SV_Target
		{
			Include_ScreenSpaceBooleanSubtractCuboid_Fragment;
			
			UNITY_SETUP_INSTANCE_ID(i);
			fixed4 numbersCol = UNITY_ACCESS_INSTANCED_PROP(Props, _NumbersCol);

			half clip_units = step(0.001, _Clip_UV_Digits(i.uv_Units.xy, half4(i.clip_Units)));
			fixed4 units = tex2D(_NumbersTex, i.uv_Units.xy).x * clip_units;
			half unitDigits = (units);

			half clip_tens = step(0.001, _Clip_UV_Digits(i.uv_Tens.xy, half4(i.clip_Tens)));
			fixed4 tens = tex2D(_NumbersTex, i.uv_Tens.xy).x * clip_tens;
			half tensDigits = (tens);

			half clip_hundreds = step(0.001, _Clip_UV_Digits(i.uv_Hundreds.xy, half4(i.clip_Hundreds)));
			fixed4 hundreds = tex2D(_NumbersTex, i.uv_Hundreds.xy).x * clip_hundreds;
			half hundredDigits = (hundreds);

			half clip_thousands = step(0.001, _Clip_UV_Digits(i.uv_Thousands.xy, half4(i.clip_Thousands)));
			fixed4 thousands = tex2D(_NumbersTex, i.uv_Thousands.xy).x * clip_thousands;
			half thousandDigits = (thousands);

			fixed4 icon = fixed4(0, 0, 0, 0);
			fixed4 decimal = fixed4(0, 0, 0, 0);

			half allDigits = saturate(unitDigits + tensDigits + hundredDigits + thousandDigits);


			#if USE_ICON & !_MODE_NONE
				half clip_icons = step(0.001, _Clip_UV_Digits(i.uv_Icon.xy, half4(i.clip_Icon)));
				icon = tex2D(_IconsTex, i.uv_Icon.xy).x * clip_icons;
			#endif

			#if (_MODE_MONEYEARNED | USE_DECIMAL_POINT) & !_MODE_NONE
				half clip_decimal = step(0.001, _Clip_UV_Digits(i.uv_Decimal.xy, half4(i.clip_Decimal)));
				decimal = tex2D(_DecimalTex, i.uv_Decimal.xy).x * clip_decimal;
			#endif

			fixed4 output = numbersCol * (allDigits + icon + decimal);
			return output;
		}
	#endif

	#if LOCALIZED_TEXT
		#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"
		

		fixed4 frag_Localized_Text(v2f_Localized_Text i) : SV_Target
		{
			UNITY_SETUP_INSTANCE_ID(i);
			fixed4 backgroundCol = UNITY_ACCESS_INSTANCED_PROP(Props, _BackgroundColour);

			fixed4 output = tex2D(_TextTex, i.uv).x;

			half leftClip = step(i.uv_clip.x, i.uv.x);
			half rightClip = step(i.uv.x, i.uv_clip.y);
			half upClip = step(i.uv.y, i.uv_clip.z);
			half downClip = step(i.uv_clip.w, i.uv.y);

			half mask = leftClip * rightClip * upClip * downClip;

			output.x = smoothstep(0.2, 0.5, output.x) * mask;

			output.xyz = lerp(backgroundCol.xyz, _TextColour.xyz, output.x);
			output.w = max(output.w, backgroundCol.w);

			half3 worldNormal;
			_WORLD_NORMAL_FRAG_LQ(i);

			_CACHED_SHADOW_MASKED_FRAG(i);
			_LIGHT_FROM_NORMAL_WORLD(i);
			_APPLY_SHADOW_FRAG(i);


			output = _OwnLand(output, i.worldPos);
			output.rgb = _LitAlbedo(output.xyz, shadow);

			_ApplyDistanceFog(i.distanceFog, output.rgb);


			//return fixed4(mask.xxx, 1);
			return output;
		}
	#endif

	#if TIME_COUNTER
		fixed4 frag_TimeCounter(v2f_TimeCounter i) : SV_Target
		{
			half clip_minutes_ones = step(0.001, _Clip_UV_Digits(i.uv_Minutes.xy, i.clip_MinutesOnes));
			half clip_minutes_tens = step(0.001, _Clip_UV_Digits(i.uv_Minutes.zw, i.clip_MinutesTens));
			fixed4 minutesOnes = tex2D(_NumbersTex, i.uv_Minutes.xy).x * clip_minutes_ones;
			fixed4 minutesTens = tex2D(_NumbersTex, i.uv_Minutes.zw).x * clip_minutes_tens;
			half minuteDigits = (minutesTens + minutesOnes) * i.valExists.x;

			half clip_hours_ones = step(0.001, _Clip_UV_Digits(i.uv_Hours.xy, i.clip_HoursOnes));
			half clip_hours_tens = step(0.001, _Clip_UV_Digits(i.uv_Hours.zw, i.clip_HoursTens));
			fixed4 hoursOnes = tex2D(_NumbersTex, i.uv_Hours.xy).x * clip_hours_ones;
			fixed4 hoursTens = tex2D(_NumbersTex, i.uv_Hours.zw).x * clip_hours_tens;
			half hourDigits = (hoursTens + hoursOnes) * i.valExists.y;


			half clip_days_ones = step(0.001, _Clip_UV_Digits(i.uv_Days.xy, i.clip_DaysOnes));
			half clip_days_tens = step(0.001, _Clip_UV_Digits(i.uv_Days.zw, i.clip_DaysTens));
			fixed4 daysOnes = tex2D(_NumbersTex, i.uv_Days.xy).x * clip_days_ones;
			fixed4 daysTens = tex2D(_NumbersTex, i.uv_Days.zw).x * clip_days_tens;
			half dayDigits = (daysTens + daysOnes) * i.valExists.z;


			half clip_minutes_letter = step(0.001, _Clip_UV_Digits(i.uv_Minutes_Letter.xy, half4(i.clip_Minutes_Letter, 0, 1)));
			fixed4 minutesLetter = tex2D(_LettersTex, i.uv_Minutes_Letter.xy).x * clip_minutes_letter;
			half minuteLetters = (minutesLetter)*i.valExists.x;

			half clip_hours_letter = step(0.001, _Clip_UV_Digits(i.uv_Hours_Letter.xy, half4(i.clip_Hours_Letter, 0, 1)));
			fixed4 hoursLetter = tex2D(_LettersTex, i.uv_Hours_Letter.xy).x * clip_hours_letter;
			half hourLetters = (hoursLetter)*i.valExists.y;

			half clip_days_letter = step(0.001, _Clip_UV_Digits(i.uv_Days_Letter.xy, half4(i.clip_Days_Letter, 0, 1)));
			fixed4 daysLetter = tex2D(_LettersTex, i.uv_Days_Letter.xy).x * clip_days_letter;
			half dayLetters = (daysLetter)*i.valExists.z;


			//return fixed4((hourLetters + hourDigits).xxx, 1);

			fixed3 combinedDigits = fixed3(saturate(minuteDigits + minuteLetters) * _ShortCol.xyz + saturate(hourDigits + hourLetters) * _MediumCol.xyz + saturate(dayDigits + dayLetters) * _LongCol.xyz);


			combinedDigits.rgb = _LitAlbedo(combinedDigits.xyz, i.color.xxx);
			half alpha = saturate(minuteDigits + minuteLetters + hourDigits + hourLetters + dayDigits + dayLetters);
			alpha *= _Alpha;

			return fixed4(combinedDigits , alpha);
		}
	#endif
#endif

#endif // CUSTOMINC_FRAG_SHADERS