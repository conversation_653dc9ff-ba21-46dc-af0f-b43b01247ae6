// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'

#ifndef CUSTOMINC_VERT_ANIM_DEFS_INCLUDED
#define CUSTOMINC_VERT_ANIM_DEFS_INCLUDED

#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_MeshParticles.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_VertAnimFunctions.cginc"


#define SHADOW_COORDS_VA(idx1) //unityShadowCoord4 _ShadowCoord : TEXCOORD##idx1;
#define TRANSFER_SHADOW_VA(a) //a._ShadowCoord = ComputeScreenPos(a.pos);


float4 _VertAnimPos_TexelSize;

half _AnimSpeed;

sampler2D _MainTex;

sampler2D _WaterBackgroundTexture;
half _RefractionAmountClose;
half _RefractionAmountFar;
half _RefractionDistanceClose;
half _RefractionDistanceFar;
half _RefractionStrength;

sampler2D _ErosionTex;
half _ErodeFrequency;
half _ErodeDelay;
half _ErodeVDMagnitude;
sampler2D _UtilityTex;
sampler2D _VertAnimNorm;
sampler2D _VertAnimPos;
half _WindDisplacementAmount;
	
#if _VA_INSTANCING_ON
	half _AnimMagnitude;
	UNITY_INSTANCING_BUFFER_START(PropsVA)
		UNITY_DEFINE_INSTANCED_PROP(fixed4, _InstanceColor)
		UNITY_DEFINE_INSTANCED_PROP(fixed4, _TintColour)
		UNITY_DEFINE_INSTANCED_PROP(half, _BrightnessRand)
		UNITY_DEFINE_INSTANCED_PROP(half, _AnimProgress)
		UNITY_DEFINE_INSTANCED_PROP(half, _Erode)
	UNITY_INSTANCING_BUFFER_END(PropsVA)
#elif _VA_INSTANCING_OFF
	half _AnimProgress;
	half _Erode;
	half _AnimMagnitude;
	fixed4 _TintColour;
#endif
half _AdjustStart;
half _AdjustEnd;

fixed4 _Tint;

half _SpecularPower;

half _Reflection;
half _Smoothness;

half _FresnelPower;
half _FresnelExponent;
half _FadeSpeed;


// toggles
half _FadeOnEnd;
half _FadeOnStart;
half _ContinuousAnim;
half _FresnelPerPixel;
half _NormalizeTangentsPerPixel;
half _RefractionPlane;
half _Refracts;

#define _VertAnimClip(i) \
	half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, erodeProgVert)); \
	half erosion = tex2D(_ErosionTex, i.uv_1 * _ErodeFrequency).x; \
	half visibility = i.color.x; \
	half clipVal = lerp(1, 0, erodeSlider) * visibility; \
	clipVal = clipVal - erosion - (1 - i.specularRemoveSlider); \
	clip(clipVal)

#if _VA_INSTANCING_ON

	struct v2f_VA {
		fixed4 color : COLOR0;
		V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
		half2 uv_1 : TEXCOORD1;
		half2 uv_2 : TEXCOORD2;
		half3 worldNormal : TEXCOORD3;
		float4 worldPos : TEXCOORD4;
		float4 screenPos : TEXCOORD5;
		half3 specular : TEXCOORD6;
		fixed specularRemoveSlider : TEXCOOR7;
		half refractionAmount : TEXCOORD8;
		fixed fresnel : TEXCOORD9;
		_DISTANCE_VARS(10)
		_LIGHTING_VARS(1, 11)
		float4 uvgrab : TEXCOORD12;
		SHADOW_COORDS_VA(14)
		UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	struct v2f_VA_ShadowCaster {
		fixed4 color : COLOR0;
		V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
		half2 uv_1 : TEXCOORD1;
		half2 uv_2 : TEXCOORD2;
		half3 worldNormal : TEXCOORD3;
		float4 worldPos : TEXCOORD4;
		float4 screenPos : TEXCOORD5;
		half3 specular : TEXCOORD6;
		fixed specularRemoveSlider : TEXCOOR7;
		half refractionAmount : TEXCOORD8;
		fixed fresnel : TEXCOORD9;
		_LIGHTING_VARS(1, 11)
			UNITY_VERTEX_INPUT_INSTANCE_ID
	};

	struct v2f_VA_Particle
{
	fixed4 color : COLOR0;
	V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
	half2 uv : TEXCOORD1;
	half2 uv_1 : TEXCOORD2;
	_LIGHTING_VARS(1, 3)
	_WORLDPOS_VARS(4)
	_DISTANCE_VARS(5)
	_NTB_VARS(6, 7, 8)
	SHADOW_COORDS_VA(9)
};
	
	struct v2f_VA_Particle_ShadowCaster
{
	fixed4 color : COLOR0;
	V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
	half2 uv : TEXCOORD1;
	half2 uv_1 : TEXCOORD2;
	_LIGHTING_VARS(1, 3)
	_WORLDPOS_VARS(4)
	_DISTANCE_VARS(5)
	_NTB_VARS(6, 7, 8)
};
	
	struct v2f_VA_MeshInstance
{
	fixed4 color : COLOR0;
	V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
	half2 uv : TEXCOORD1;
	half2 uv_1 : TEXCOORD2;
	half2 uv_2 : TEXCOORD3;
	_LIGHTING_VARS(1, 4)
	_WORLDPOS_VARS(5)
	_DISTANCE_VARS(6)
	_NTB_VARS(7, 8, 9)
	SHADOW_COORDS_VA(10)
	half3 worldNormal : TEXCOORD11;
	UNITY_VERTEX_INPUT_INSTANCE_ID
};
	
	struct appdata_VA_Particle
{
	float4 vertex : POSITION;
	float4 TX0 : TEXCOORD0;
	float4 TX1 : TEXCOORD1;
	float4 TX2 : TEXCOORD2;
	float4 TX3 : TEXCOORD3;
	float4 TX4 : TEXCOORD4;
	half3 normal : NORMAL;
	fixed4 color : COLOR;
	half4 tangent : TANGENT;
};
	
	




	

	v2f_VA vert_VA (appdata_instancing v)
	{
		v2f_VA o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA, o);
		UNITY_SETUP_INSTANCE_ID(v);
		UNITY_TRANSFER_INSTANCE_ID(v, o);
		o.uv_2 = v.uv_2 ;
		o.uv_1 = v.uv;


		half animProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _AnimProgress);
		half erodeProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _Erode);

		
		half2 animationUVs = _CalAnimUVs(_AdjustStart, _AdjustEnd, _AnimSpeed, animProgVert, o.uv_2);


		_CalcUtils(o);

		half3 objectNormal = normalize(_VertAnimToNormal(_VertAnimNorm, animationUVs));
		objectNormal.xyz = fixed3(-objectNormal.x, objectNormal.yz);

		half3 worldNormal = normalize(mul(UNITY_MATRIX_M, half4(objectNormal, 0)));
		o.worldNormal = worldNormal;

		half3 lightDirection = _WorldSpaceLightPos0;

		half shadow = max(saturate(dot(objectNormal, lightDirection)), 0.0);

		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);
				
		half noiseFrequency = 0.5;
		half3 droppingOffset = (tex2Dlod(_ErosionTex, float4(float2(v.vertex.x * noiseFrequency, v.vertex.y * noiseFrequency + v.vertex.z * noiseFrequency) , 0, 0)).xxx - 0.4) * 2;
		droppingOffset *= o.worldNormal * _ErodeVDMagnitude;
				
		half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, erodeProgVert));
		
					 
		animOffset = lerp(animOffset, animOffset + droppingOffset, erodeSlider);

		v.vertex.xyz += animOffset.xyz;

		o.worldPos = mul(UNITY_MATRIX_M, v.vertex);
		
		float3 vertDisp = _WindTreeDisplacement(v.vertex, o.worldPos) * _WindDisplacementAmount;
		v.vertex.xyz += vertDisp.xyz;
		_CalculateDistanceFog(v.vertex, o.distanceFog);

		o.pos = UnityObjectToClipPos(v.vertex);


		o.screenPos = _ComputeScreenPos(o.pos);
		float3 worldViewDir = normalize(UnityWorldSpaceViewDir(o.worldPos));

		o.fresnel = 1-saturate(pow(abs(dot(worldNormal, -worldViewDir)), _FresnelExponent) * _FresnelPower);

		#if FLIP_FRESNEL
			o.fresnel = 1 - o.fresnel;
		#endif

		o.specularRemoveSlider = 1;
		#if _FADE_ON_START
			half fadeOnStart = saturate(animProgVert * _FadeSpeed);
			o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnStart);
		#endif
		#if _FADE_ON_END
			half fadeOnEnd = 1 - saturate((animProgVert * _FadeSpeed) - (_FadeSpeed - 1));
			o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnEnd);
		#endif


		half3 specular = half3(0,0,0);//TODO saturate(_Specularity_BlinnPhong(o.worldPos, o.worldNormal, _SpecularPower, _SpecularColor)).xyz * visibility * vertFade;
	
		specular *= o.specularRemoveSlider;
		o.fresnel *= o.specularRemoveSlider;
	

		_DOT_LIGHT_VERT;
		_CACHED_SHADOW_VERT(o);

		o.uvgrab = ComputeGrabScreenPosHDRP(v.vertex);
		o.specular = specular;

		float3 cameraWorldPos = _WorldSpaceCameraPos;
		o.refractionAmount = _CameraDistanceLerpVal(o.worldPos, _RefractionDistanceClose, _RefractionDistanceFar, _RefractionAmountClose, _RefractionAmountFar);
		TRANSFER_SHADOW_VA(o)
		return o;
	}

	v2f_VA_ShadowCaster vert_VA_ShadowCaster (appdata_instancing v)
	{
		v2f_VA_ShadowCaster o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA_ShadowCaster, o);
		UNITY_SETUP_INSTANCE_ID(v);
		UNITY_TRANSFER_INSTANCE_ID(v, o);
		o.uv_2 = v.uv_2 ;
		o.uv_1 = v.uv;
		half animProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _AnimProgress);
		half erodeProgVert = UNITY_ACCESS_INSTANCED_PROP(PropsVA, _Erode);
		half2 animationUVs = _CalAnimUVs(_AdjustStart, _AdjustEnd, _AnimSpeed, animProgVert, o.uv_2);


		_CalcUtils(o);

		half3 objectNormal = normalize(_VertAnimToNormal(_VertAnimNorm, animationUVs));
		objectNormal.xyz = fixed3(-objectNormal.x, objectNormal.yz);

		float3 worldNormal = normalize(mul((float3x3)UNITY_MATRIX_M, objectNormal));
		o.worldNormal = worldNormal;

		half3 lightDirection = _WorldSpaceLightPos0;

		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);
				
		half noiseFrequency = 0.5;
		half3 droppingOffset = (tex2Dlod(_ErosionTex, float4(float2(v.vertex.x * noiseFrequency, v.vertex.y * noiseFrequency + v.vertex.z * noiseFrequency) , 0, 0)).xxx - 0.4) * 2;
		droppingOffset *= o.worldNormal * _ErodeVDMagnitude;
				
		half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, erodeProgVert));
					
		animOffset = lerp(animOffset, animOffset + droppingOffset, erodeSlider);

		v.vertex.xyz += animOffset.xyz;

		float4 worldPos = mul(UNITY_MATRIX_M, v.vertex);
		float4 vertDisp = _WindTreeDisplacement(v.vertex, worldPos) * _WindDisplacementAmount;
		v.vertex.xyz += vertDisp.xyz;


		o.specularRemoveSlider = 1;
		half fadeOnStart = saturate(animProgVert * _FadeSpeed);
		half fadeOnEnd = 1 - saturate((animProgVert * _FadeSpeed) - (_FadeSpeed - 1));

		fadeOnStart = lerp(1, fadeOnStart, _FadeOnStart);
		fadeOnEnd = lerp(1, fadeOnEnd, _FadeOnEnd);

		o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnStart);
		o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnEnd);

		//float4 offset = mul(UNITY_MATRIX_M, vertDisp * (float4(0.65, 1,1,1)));


		//_InputVertexOffset(v.vertex, vertDisp);


		TRANSFER_SHADOW_CASTER(o)



		return o;
	}

	v2f_VA_Particle vert_VA_Particle(appdata_VA_Particle v)
	{
		v2f_VA_Particle o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA_Particle, o);
		o.uv = v.TX0.xy;
		o.uv_1 = v.TX0.zw;
		float2 animationUVs = o.uv_1.xy;
		half particleAge = v.TX1.x;
		float3 velocity = normalize(float3(v.TX3.xyz));
		velocity.x *= -1;
		half3 angleXYZ = _CalculateRotationFromVelocity(velocity);
		float3 particleCentre = float3(v.TX2.xyz);
		half3 particleSize = v.TX1.xyz;
		half randomOffset = v.TX2.w;


		#if _ANIMMODE
			animationUVs.y = frac(animationUVs.y + randomOffset - (_AnimSpeed * _Time.y));
		#else
			animationUVs.y = frac(animationUVs.y + randomOffset - (_AnimSpeed * particleAge));
		#endif

		#if _DEBUG
			animationUVs.y = frac(_AnimProgress);
		#endif

		_WORLD_NORMAL_VERT(v);
		//o.test = v.normal;

		fixed3 tint = _Tint.xyz * v.color.xyz * _Brightness;



		o.color.xyz = tint; //* o.uv_1.xxx;
		o.color.w = v.color.w;




		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);

		animOffset *= particleSize;
		animOffset.z *= -1;

		v.vertex.xyz += float3(animOffset.x * 0.25, animOffset.y, animOffset.z);



		v.vertex.xyz -= particleCentre;
		v.vertex.xyz = _RotateVertsByAngleXYZ(v.vertex, angleXYZ);
		v.vertex.xyz += particleCentre;
	

		half nl = 1;  // cancelling out vert shading in favour of per pixel shading
		_CALC_WORLDPOS(o, v);
		_CACHED_SHADOW_VERT(o);
		_CalculateDistanceFog(v.vertex, o.distanceFog);
		_CALC_NTB(o, v);

		///o.test = v.normal;

		o.pos = UnityObjectToClipPos(v.vertex);
		TRANSFER_SHADOW_VA(o)
		return o;
	}

	v2f_VA_Particle vert_VA_Particle_ShadowCaster(appdata_VA_Particle v)
	{
		v2f_VA_Particle o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA_Particle, o);
		o.uv = v.TX0.xy;
		o.uv_1 = v.TX0.zw;
		half particleAge = v.TX1.x;
		float3 velocity = normalize(float3(v.TX3.xyz));
		velocity.x *= -1;
		half3 angleXYZ = _CalculateRotationFromVelocity(velocity);
		float3 particleCentre = float3(v.TX2.xyz);
		half3 particleSize = v.TX1.xyz;
		half randomOffset = v.TX2.w;

		half2 animationUVs = o.uv_1.xy;
		#if _ANIMMODE
			animationUVs.y = frac(animationUVs.y + randomOffset - (_AnimSpeed * _Time.y));
		#else
			animationUVs.y = frac(animationUVs.y + randomOffset - (_AnimSpeed * particleAge));
		#endif

		#if _DEBUG
			animationUVs.y = frac(_AnimProgress);
		#endif

		_WORLD_NORMAL_VERT(v);
		//o.test = v.normal;

		fixed3 tint = _Tint.xyz * v.color.xyz * _Brightness;



		o.color.xyz = tint; //* o.uv_1.xxx;
		o.color.w = v.color.w;




		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);

		animOffset *= particleSize;
		animOffset.z *= -1;

		v.vertex.xyz += float3(animOffset.x * 0.25, animOffset.y, animOffset.z);



		v.vertex.xyz -= particleCentre;
		v.vertex.xyz = _RotateVertsByAngleXYZ(v.vertex, angleXYZ);
		v.vertex.xyz += particleCentre;

		half nl = 1;  // cancelling out vert shading in favour of per pixel shading
		_CALC_WORLDPOS(o, v);

		///o.test = v.normal;

		o.pos = UnityObjectToClipPos(v.vertex);
		TRANSFER_SHADOW_CASTER(o)

		return o;
	}

#elif _VA_INSTANCING_OFF

	struct v2f_VA {
		fixed4 color : COLOR0;
		V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
		half2 uv_1 : TEXCOORD1;
		half2 uv_2 : TEXCOORD2;
		half3 worldNormal : TEXCOORD3;
		float4 worldPos : TEXCOORD4;
		float4 screenPos : TEXCOORD5;
		half3 specular : TEXCOORD6;
		fixed specularRemoveSlider : TEXCOOR7;
		half refractionAmount : TEXCOORD8;
		fixed fresnel : TEXCOORD9;
		_DISTANCE_VARS(10)
		_LIGHTING_VARS(1, 11)
			float4 uvgrab : TEXCOORD12;
		SHADOW_COORDS_VA(14)
	};
	
	struct v2f_VA_ShadowCaster {
	fixed4 color : COLOR0;
	V2F_SHADOW_CASTER;//float4 pos : SV_POSITION;
	half2 uv_1 : TEXCOORD1;
	half2 uv_2 : TEXCOORD2;
	half3 worldNormal : TEXCOORD3;
	float4 worldPos : TEXCOORD4;
	float4 screenPos : TEXCOORD5;
	half3 specular : TEXCOORD6;
	fixed specularRemoveSlider : TEXCOOR7;
	half refractionAmount : TEXCOORD8;
	fixed fresnel : TEXCOORD9;
	_LIGHTING_VARS(1, 11)
	UNITY_VERTEX_INPUT_INSTANCE_ID
};





	v2f_VA vert_VA (appdata v)
	{
		v2f_VA o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA, o);
		o.uv_2 = v.uv_2 ;
		o.uv_1 = v.uv;

		half animProgVert = _AnimProgress;

		half2 animationUVs = _CalAnimUVs(_AdjustStart, _AdjustEnd, _AnimSpeed, animProgVert, o.uv_2);


		_CalcUtils(o);


		
		
		
		
		half3 objectNormal = normalize(_VertAnimToNormal(_VertAnimNorm, animationUVs));
		objectNormal.xyz = fixed3(-objectNormal.x, objectNormal.yz);

		half3 worldNormal = normalize(mul(UNITY_MATRIX_M, half4(objectNormal, 0)));
		o.worldNormal = worldNormal;

		half3 lightDirection = _WorldSpaceLightPos0;

		half shadow = max(saturate(dot(objectNormal, lightDirection)), 0.0);

		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);
				
		half noiseFrequency = 0.5;
		half3 droppingOffset = (tex2Dlod(_ErosionTex, float4(float2(v.vertex.x * noiseFrequency, v.vertex.y * noiseFrequency + v.vertex.z * noiseFrequency) , 0, 0)).xxx - 0.4) * 2;
		droppingOffset *= o.worldNormal * _ErodeVDMagnitude;
				
		half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, _Erode));
		
					 
		animOffset = lerp(animOffset, animOffset + droppingOffset, erodeSlider);

		v.vertex.xyz += animOffset.xyz;

		o.pos = UnityObjectToClipPos(v.vertex);
		o.worldPos = mul(UNITY_MATRIX_M, v.vertex);
		o.screenPos = _ComputeScreenPos(o.pos);
		float3 worldViewDir = normalize(UnityWorldSpaceViewDir(o.worldPos));

		o.fresnel = 1-saturate(pow(abs(dot(worldNormal, -worldViewDir)), _FresnelExponent) * _FresnelPower);

		#if FLIP_FRESNEL
			o.fresnel = 1 - o.fresnel;
		#endif

		o.specularRemoveSlider = 1;
		#if _FADE_ON_START
			half fadeOnStart = saturate(animProgVert * _FadeSpeed);
			o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnStart);
		#endif
		#if _FADE_ON_END
			half fadeOnEnd = 1 - saturate((animProgVert * _FadeSpeed) - (_FadeSpeed - 1));
			o.specularRemoveSlider = min(o.specularRemoveSlider, fadeOnEnd);
		#endif

		half3 specular = half3(0,0,0);//TODO saturate(_Specularity_BlinnPhong(o.worldPos, o.worldNormal, _SpecularPower, _SpecularColor)).xyz * visibility * vertFade;
	
		specular *= o.specularRemoveSlider;
		o.fresnel *= o.specularRemoveSlider;
	

		half nl = 1;
		_CACHED_SHADOW_VERT(o);

		o.uvgrab = ComputeGrabScreenPosHDRP(o.pos);
		o.specular = specular;

		float3 cameraWorldPos = _WorldSpaceCameraPos;
		o.refractionAmount = _CameraDistanceLerpVal(o.worldPos, _RefractionDistanceClose, _RefractionDistanceFar, _RefractionAmountClose, _RefractionAmountFar);
		return o;
	}

	v2f_VA_ShadowCaster vert_VA_ShadowCaster (appdata v)
	{
		v2f_VA_ShadowCaster o;
		UNITY_INITIALIZE_OUTPUT(v2f_VA_ShadowCaster, o);
		o.uv_2 = v.uv_2 ;
		o.uv_1 = v.uv;

		half2 animationUVs = o.uv_2;
		animationUVs.y  = _RangeRemapFloat(0, 1, _AdjustStart, _AdjustEnd, _AnimProgress);
		animationUVs.y = 1 - animationUVs.y;

		_CalcUtils(o);

		half3 objectNormal = normalize(_VertAnimToNormal(_VertAnimNorm, animationUVs));
		objectNormal.xyz = fixed3(-objectNormal.x, objectNormal.yz);

		float3 worldNormal = normalize(mul((float3x3)UNITY_MATRIX_M, objectNormal));
		o.worldNormal = worldNormal;


		float3 animOffset = _VertAnimToVector(_VertAnimPos, animationUVs, _AnimMagnitude);
				
		half noiseFrequency = 0.5;
		half3 droppingOffset = (tex2Dlod(_ErosionTex, float4(float2(v.vertex.x * noiseFrequency, v.vertex.y * noiseFrequency + v.vertex.z * noiseFrequency) , 0, 0)).xxx - 0.4) * 2;
		droppingOffset *= o.worldNormal * _ErodeVDMagnitude;
				
		half erodeSlider = saturate(_RangeRemapFloat(_ErodeDelay, 1, 0, 1, _Erode));
					
		animOffset = lerp(animOffset, animOffset + droppingOffset, erodeSlider);

		v.vertex.xyz += animOffset.xyz;

		o.pos = UnityObjectToClipPos(v.vertex);
	
		TRANSFER_SHADOW_CASTER(o)
		return o;
	}

#endif 




#endif //  CUSTOMINC_VERT_ANIM_DEFS_INCLUDED