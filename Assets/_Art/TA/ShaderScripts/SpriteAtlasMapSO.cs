using System.Collections.Generic;
using UnityEngine;
using UnityEngine.U2D;

#if UNITY_EDITOR

using UnityEditor;

[CustomEditor(typeof(SpriteAtlasMapSO))]
public class SpriteAtlasMapSOEditor : Editor {

	private static int spriteAtlasExtensionLength = ".spriteatlas".Length;

	private static bool includeResourceFolderSpriteAtlasesOnly = true;

	public override void OnInspectorGUI() {
		base.OnInspectorGUI();

		if(GUILayout.Button("Refresh")) {
			SpriteAtlasMapSO spriteAtlasMapSO = (SpriteAtlasMapSO)target;

			Dictionary<string, string> atlasNameBySpriteName;
			int duplicateNumber = CreateAtlasMap(out atlasNameBySpriteName);
			if(duplicateNumber > 0) {
				Debug.LogError("Didn't save new sprite atlas mapping, because there were a few duplicate sprites. Check above. Amount: " + duplicateNumber);
			} else {
				spriteAtlasMapSO.Setup(atlasNameBySpriteName);
				EditorUtility.SetDirty(spriteAtlasMapSO);
			}

		}
	}

	private static int CreateAtlasMap(out Dictionary<string, string> atlasNameBySpriteName) {
		atlasNameBySpriteName = new Dictionary<string, string>();

		int duplicateNumber = 0;

		string[] spriteAtlasGuids = AssetDatabase.FindAssets("t:spriteatlas");
		foreach(string saGuid in spriteAtlasGuids) {
			string trimmedAtlasPath = null;

			string atlasPath = AssetDatabase.GUIDToAssetPath(saGuid);
			if(includeResourceFolderSpriteAtlasesOnly) {
				const string folderName = "/Resources/";
				int index = atlasPath.LastIndexOf(folderName);
				if(index > 0) {
					string newPath = atlasPath.Substring(index + folderName.Length);
					newPath = newPath.Substring(0, newPath.Length - spriteAtlasExtensionLength);
					trimmedAtlasPath = newPath;
				}
			} else {
				trimmedAtlasPath = atlasPath;
			}

			if(!string.IsNullOrEmpty(trimmedAtlasPath)) {
				SpriteAtlas spriteAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
				Sprite[] sprites = new Sprite[spriteAtlas.spriteCount];
				spriteAtlas.GetSprites(sprites);

				foreach(Sprite sprite in sprites) {
					string spriteName = sprite.name;
					string fullSpriteName = spriteName.Substring(0, spriteName.IndexOf("(Clone)"));

					if(atlasNameBySpriteName.ContainsKey(fullSpriteName)) {
						Debug.LogError("Duplicate: " + fullSpriteName + " in atlasA: " + atlasPath + " atlasB: " + atlasNameBySpriteName[fullSpriteName]);
						++duplicateNumber;
					} else {
						atlasNameBySpriteName.Add(fullSpriteName, trimmedAtlasPath);
					}

					GameObject.DestroyImmediate(sprite);
				}
			}

		}

		return duplicateNumber;
	}

	[MenuItem("Tools/Sprite Atlas/Create Mapping")]
	public static void Create() {
		Dictionary<string, string> atlasNameBySpriteName;

		int duplicateNumber = CreateAtlasMap(out atlasNameBySpriteName);

		if(duplicateNumber > 0) {
			Debug.LogError("Didn't save new sprite atlas mapping, because there were a few duplicate sprites. Check above. Amount: " + duplicateNumber);
		} else {
			SpriteAtlasMapSO spriteAtlasMapSO = ScriptableObject.CreateInstance<SpriteAtlasMapSO>();
			spriteAtlasMapSO.Setup(atlasNameBySpriteName);
			AssetDatabase.CreateAsset(spriteAtlasMapSO, "Assets/Sprite_Atlas_Mapping.asset");
		}

	}

}

#endif

public class SpriteAtlasMapSO : ScriptableObject, ISerializationCallbackReceiver {

	[SerializeField]
	private List<string> spriteNames = new List<string>();

	[SerializeField]
	private List<string> atlasNames = new List<string>();

	private Dictionary<string, string> atlasNameBySpriteName = new Dictionary<string, string>();

	public void OnAfterDeserialize() {
		atlasNameBySpriteName.Clear();
		int dictLength = spriteNames.Count;
		for(int i = 0; i < dictLength; ++i) {
			atlasNameBySpriteName.Add(spriteNames[i], atlasNames[i]);
		}
	}

	public void OnBeforeSerialize() {
		spriteNames.Clear();
		atlasNames.Clear();
		foreach(var kvp in atlasNameBySpriteName) {
			spriteNames.Add(kvp.Key);
			atlasNames.Add(kvp.Value);
		}
	}

	public void Setup(Dictionary<string, string> atlasNameBySpriteName) {
		spriteNames.Clear();
		atlasNames.Clear();
		this.atlasNameBySpriteName = atlasNameBySpriteName;
	}

	public string FindSpriteAtlasPathBySpriteName(string spriteName) {
		string result;
		atlasNameBySpriteName.TryGetValue(spriteName, out result);
		return result;
	}

}