Shader "_Scenery/S_Glass"
{
     Properties
    {
		//[Header(Core)]
		_Transparency("Transparency", Range(0.0,1.0)) = 1
		_Reflection("Reflection", Range(0.0,1.0)) = 1
        _RefractionStrength("Refraction Strength", Range(-10.0,10.0)) = 0
		_MaskTexture("Normal Map", 2D) = "bump" {}

		_SpecularColor("Specular Color",Color) = (1,1,1,1)
		_SpecularPower("Specular Power", Float) = 1
		_SpecularIntensity("Specular Intensity", Float) = 2
		//[Header(Fresnel)]
		_FresnelColor ("Fresnel Color", Color ) = (0.5,0.5,0.5,1)
		_FresnelPower("Fresnel Power", Range(0.0,10.0)) = 2
		_FresnelExponent("Fresnel Exponent", Range(0.001, 30)) = 2
		//[Toggle(FLIP_FRESNEL)] _FlipFresnel("Flip Fresnel",Float) = 1
		[Toggle(FRESNEL_PER_PIXEL)] _FresnelPerPixel("Per Pixel Fresnel",Float) = 0
			
		[Toggle(USE_SMOOTHNESS)] _UseSmoothness("Use Smoothness Parameter",Float) = 1
		_Smoothness("Smoothness", Range(0,1)) = 0.915


		//[Header(Dirt)]
		[Toggle(_DIRT)] _Dirt("Enable Dirt",Float) = 0
		_DirtMask("Dirt Mask",2D) = "White" {}
		_DirtSlider("Dirt Strength",Range(0.0,1.0)) = 0.2
		//
		//Shadows
		_ShadowPull("Shadow Distance Adjustment", Float) = 1.01
		[Toggle(NORMALIZE_TANGENTS_PER_PIXEL)] _NormalizeTangentsPerPixel("Normalize Tangents Per Pixel",Float) = 0
		[Toggle(CAST_SHADOWS)] _CastShadows("Cast Shadows",Float) = 1
		_AffectedByDistricts("Affected By Districts", Range(0, 1)) = 1

		_TintWindow1("Tint Window 1 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow2("Tint Window 2 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow3("Tint Window 3 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow4("Tint Window 4 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow5("Tint Window 5 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow6("Tint Window 6 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow7("Tint Window 7 (u1,v1,u2,v2)", Vector) = (0,0,0,0)
        _TintWindow8("Tint Window 8 (u1,v1,u2,v2)", Vector) = (0,0,0,0)

        _TintColour1("Tint colour 1", Color) = (1,1,1,0)
        _TintColour2("Tint colour 2", Color) = (1,1,1,0)
        _TintColour3("Tint colour 3", Color) = (1,1,1,0)
        _TintColour4("Tint colour 4", Color) = (1,1,1,0)
        _TintColour5("Tint colour 5", Color) = (1,1,1,0)
        _TintColour6("Tint colour 6", Color) = (1,1,1,0)
        _TintColour7("Tint colour 7", Color) = (1,1,1,0)
        _TintColour8("Tint colour 8", Color) = (1,1,1,0)
			  
        _UVOffset1("UV Offset 1 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset2("UV Offset 2 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset3("UV Offset 3 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset4("UV Offset 4 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset5("UV Offset 5 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset6("UV Offset 6 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset7("UV Offset 7 (u1,v1,u2,v2))", Vector) = (0,0,0,0)
        _UVOffset8("UV Offset 8 (u1,v1,u2,v2))", Vector) = (0,0,0,0)

        _AltTextureAtlas("Alt Texture Atlas", 2D) = "white" {}

        _AltTexture1("Alt Texture 1", 2D) = "white" {}
        _AltTexture2("Alt Texture 2", 2D) = "white" {}
        _AltTexture3("Alt Texture 3", 2D) = "white" {}
        _AltTexture4("Alt Texture 4", 2D) = "white" {}
        _AltTexture5("Alt Texture 5", 2D) = "white" {}
        _AltTexture6("Alt Texture 6", 2D) = "white" {}
        _AltTexture7("Alt Texture 7", 2D) = "white" {}
        _AltTexture8("Alt Texture 8", 2D) = "white" {}

        _AltTextureWeight1("Alt Texture Weight 1", float) = 0
        _AltTextureWeight2("Alt Texture Weight 2", float) = 0
        _AltTextureWeight3("Alt Texture Weight 3", float) = 0
        _AltTextureWeight4("Alt Texture Weight 4", float) = 0
        _AltTextureWeight5("Alt Texture Weight 5", float) = 0
        _AltTextureWeight6("Alt Texture Weight 6", float) = 0
        _AltTextureWeight7("Alt Texture Weight 7", float) = 0
        _AltTextureWeight8("Alt Texture Weight 8", float) = 0

        _AltTextureAtlasTransform1("Alt Texture Atlas Transform 1", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform2("Alt Texture Atlas Transform 2", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform3("Alt Texture Atlas Transform 3", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform4("Alt Texture Atlas Transform 4", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform5("Alt Texture Atlas Transform 5", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform6("Alt Texture Atlas Transform 6", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform7("Alt Texture Atlas Transform 7", Vector) = (1,1,0,0)
        _AltTextureAtlasTransform8("Alt Texture Atlas Transform 8", Vector) = (1,1,0,0)
		
		_cuboidCenter1("_cuboidCenter1", Vector) = (0,0,0,0)
		_cuboidExtents1a("_cuboidExtents1a", Vector) = (0,1,0,0)
		_cuboidExtents1b("_cuboidExtents1b", Vector) = (0,1,0,0)
		_cuboidExtents1c("_cuboidExtents1c", Vector) = (0,1,0,0)
		_cuboidCenter2("_cuboidCenter2", Vector) = (0,0,0,0)
		_cuboidExtents2a("_cuboidExtents2a", Vector) = (0,1,0,0)
		_cuboidExtents2b("_cuboidExtents2b", Vector) = (0,1,0,0)
		_cuboidExtents2c("_cuboidExtents2c", Vector) = (0,1,0,0)
		_cuboidCenter3("_cuboidCenter3", Vector) = (0,0,0,0)
		_cuboidExtents3a("_cuboidExtents3a", Vector) = (0,1,0,0)
		_cuboidExtents3b("_cuboidExtents3b", Vector) = (0,1,0,0)
		_cuboidExtents3c("_cuboidExtents3c", Vector) = (0,1,0,0)
		_cuboidColor("_cuboidColor", Color) = (1,0,1,0)

        // GBuffer
        [HideInInspector] _StencilRefGBuffer("_StencilRefGBuffer", Int) = 2 // StencilUsage.RequiresDeferredLighting
        [HideInInspector] _StencilWriteMaskGBuffer("_StencilWriteMaskGBuffer", Int) = 3 // StencilUsage.RequiresDeferredLighting | StencilUsage.SubsurfaceScattering
    }

	// LOD 300
    SubShader
    {
		Tags { "RenderPipeline" = "UniversalPipeline" "UniversalMaterialType" = "Lit" "RenderType" = "Transparent" "Queue" = "Transparent" }
		//Blend SrcAlpha OneMinusSrcAlpha
        LOD 300

		/*GrabPass
		{
			"_WaterBackgroundTexture"
			Tags {"Queue" = "Transparent+1000" }
		}*/

        Pass
        {
			Tags { "LightMode"="UniversalForward" }
			HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			#pragma multi_compile_local __ _PATTERNED
			#pragma shader_feature_local _DIRT
			#pragma shader_feature_local NORMALIZE_TANGENTS_PER_PIXEL
			#pragma shader_feature_local FRESNEL_PER_PIXEL
			//#pragma shader_feature_local FLIP_FRESNEL
			#pragma shader_feature_local USE_SMOOTHNESS
			#pragma shader_feature __ CAST_SHADOWS
    		#pragma multi_compile_local _ CSGCUBOID_ADDITION
			#pragma multi_compile_local _ _CIRCLECLIP
			#pragma target 3.5

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

			#include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			//
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"


			//Fresnel
			FresnelDefs;

			//Dirt
			sampler2D _DirtMask;
			float4 _DirtMask_ST;
			half _DirtSlider;

			//Refraction
			half _RefractionStrength;

			//Util
			half _Transparency;
			sampler2D _MaskTexture;
			float4 _MaskTexture_ST;
			half _Reflection;

			half _ShadowDistanceAdjustment;
			Base_Defs;
			

			half _SpecularPower;

			half _Smoothness;

            struct Input
            {
			    float4 vertex : SV_POSITION;
				float2 refracuv : TEXCOORD0;
				float4 uvgrab : TEXCOORD1;
				fixed fresnel : TEXCOORD3;
				_WORLDPOS_VARS(4)
				_NTB_VARS(5, 6, 7)
				_TINT_WINDOW_INPUT_VARS(8,9,10, 11) 
				SHADOW_COORDS(12)
				float2 dirtuv : TEXCOORD13;
				_LIGHTING_VARS(0, 14)
				_DISTANCE_VARS(15)
				_WORLDNORMAL_VARS(16)
				_PREMIUM_VARS(17)
            };

            Input vert (appdata_full v)
            {
                Input o;
				TintWindow_Vertex;
                o.vertex = UnityObjectToClipPos(v.vertex);
				half nl = 1;  // cancelling out vert shading in favour of per pixel shading
				_CALC_WORLDPOS(o, v);
				_CALC_NTB(o, v);
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);
				o.uvgrab = ComputeGrabScreenPosHDRP(v.vertex);
                o.refracuv = TRANSFORM_TEX(v.texcoord, _MaskTexture);

				o.dirtuv = TRANSFORM_TEX(v.texcoord, _DirtMask);

				_CALC_WORLDVIEWDIR(o, v);
				//Fresnel
				_CALC_Fresnel(o, v);

				#if FLIP_FRESNEL
					o.fresnel = 1 - o.fresnel;
				#endif

				TRANSFER_SHADOW(o)
                return o;
            }
			
            fixed4 frag (Input i) : SV_Target
            {
	            Include_ScreenSpaceBooleanSubtractCuboid_Fragment;

				half3 worldViewDir = _WorldViewDir(i.worldPos);
				half3 worldNormal = UnpackNormal(tex2D(_MaskTexture, i.refracuv)).xyz;
				
				

				_WORLD_NORMAL_FRAG(i);
				  

				float3 normalDirection = normalize(half3(i.tspace0.z, i.tspace1.z, i.tspace2.z));

				half3 worldReflect = reflect(-worldViewDir,worldNormal);

				#if USE_SMOOTHNESS
					fixed3 skyColor = _ReflectCubemap_LOD(worldReflect,(1-_Smoothness ) * 5);
				#else
					fixed3 skyColor = _ReflectCubemap(worldReflect);
				#endif

				fixed4 refracCol = _RefractionNormal(worldReflect,i.uvgrab, _RefractionStrength);

				fixed4 baseCol;
				fixed3 tintedCol;

				#ifdef _PATTERNED
					tintedCol = tex2D(_AltTextureAtlas, i.atlasUV).xyz;
				#else 
					tintedCol = i.tint.xyz;
				#endif

				#if FRESNEL_PER_PIXEL
					_CALC_Fresnel_Pixel(i, worldNormal);
					#if FLIP_FRESNEL
						fresnel = 1 - fresnel;
					#endif // FLIP_FRESNEL
					i.fresnel = fresnel;
				#endif // FRESNEL_PER_PIXEL


				baseCol.xyz = lerp(tintedCol, _FresnelColor.xyz, i.fresnel);

				float smoothness = 1;
				#if _DIRT
					fixed dirtCol = tex2D(_DirtMask, i.dirtuv).x;
					smoothness  *= dirtCol;
					fixed dirt = lerp(1,dirtCol.x,_DirtSlider);
					refracCol.xyz = lerp(_FresnelColor,refracCol.xyz,dirt);
					skyColor = lerp(_FresnelColor,skyColor,dirt);
				#endif // _DIRT

				baseCol.rgb = lerp(baseCol.xyz, refracCol.xyz, _Transparency);
				baseCol.rgb = lerp(baseCol.rgb, skyColor, i.fresnel * _Reflection);
            	
				return float4(baseCol.rgb, 1);
            }
            ENDHLSL
        }
		//UsePass "_Shadowcasters/S_ShadowCasters/ShadowCaster_Props"
    }

	/*SubShader
    {

		Tags { "Queue" = "Transparent" "IgnoreProjector" = "True" "RenderType" = "Transparent"}
		Blend SrcAlpha OneMinusSrcAlpha
        LOD 100

		

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
			#pragma multi_compile_local __ _PATTERNED
			#pragma shader_feature_local NORMALIZE_TANGENTS_PER_PIXEL
			#pragma shader_feature_local FRESNEL_PER_PIXEL
			//#pragma shader_feature_local FLIP_FRESNEL
			#pragma shader_feature_local USE_SMOOTHNESS
			#pragma shader_feature __ CAST_SHADOWS
			#pragma target 3.5

            #include "UnityCG.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"


			//Fresnel
			fixed4 _FresnelColor;
			FresnelDefs;


			//Refraction
			half _RefractionStrength;

			//Util
			half _Transparency;
			sampler2D _MaskTexture;
			float4 _MaskTexture_ST;
			half _Reflection;

			half _ShadowDistanceAdjustment;
			Base_Defs;
			

			fixed4 _SpecularColor;
			half _SpecularPower;
			half _SpecularIntensity;

			half _Smoothness;

            struct Input
            {
			    float4 vertex : SV_POSITION;
				float2 refracuv : TEXCOORD0;
				float4 uvgrab : TEXCOORD1;
				fixed fresnel : TEXCOORD3;
				_WORLDPOS_VARS(4)
				_NTB_VARS(5, 6, 7)
				_TINT_WINDOW_INPUT_VARS(8,9,10, 11) 
				float2 dirtuv : TEXCOORD13;
				_LIGHTING_VARS(0, 14)
				_DISTANCE_VARS(15)
				_WORLDNORMAL_VARS(16)
				_PREMIUM_VARS(17)
            };

            Input vert (appdata_full v)
            {
                Input o;
				TintWindow_Vertex;
                o.vertex = UnityObjectToClipPos(v.vertex);
				half nl = 1;  // cancelling out vert shading in favour of per pixel shading
				_CALC_WORLDPOS(o, v);
				_CALC_NTB(o, v);
				_CACHED_SHADOW_VERT(o);
				_CalculateDistanceFog(v.vertex, o.distanceFog);
				o.uvgrab = ComputeGrabScreenPosHDRP(o.vertex);
                o.refracuv = TRANSFORM_TEX(v.texcoord, _MaskTexture);


				_CALC_WORLDVIEWDIR(o, v);
				//Fresnel
				_CALC_Fresnel(o, v);

				#if FLIP_FRESNEL
					o.fresnel = 1 - o.fresnel;
				#endif

                return o;
            }
			
            fixed4 frag (Input i) : SV_Target
            {
				half3 worldViewDir = _WorldViewDir(i.worldPos);
				half3 worldNormal = UnpackNormal(tex2D(_MaskTexture, i.refracuv)).xyz;
				
				

				_WORLD_NORMAL_FRAG(i);
				 

				float3 normalDirection = normalize(half3(i.tspace0.z, i.tspace1.z, i.tspace2.z));
				float3 specular = saturate(_Specularity_BlinnPhong(i.worldPos, worldNormal, _SpecularPower * 50, _SpecularColor).xyz * _SpecularIntensity);



				half3 worldReflect = reflect(-worldViewDir,worldNormal);

				#if USE_SMOOTHNESS
					fixed3 skyColor = _ReflectCubemap_LOD(worldReflect,(1-_Smoothness ) * 5);
				#else
					fixed3 skyColor = _ReflectCubemap(worldReflect);
				#endif


				fixed4 baseCol;
				fixed3 tintedCol;

				#ifdef _PATTERNED
					tintedCol = tex2D(_AltTextureAtlas, i.atlasUV).xyz;
				#else 
					tintedCol = i.tint.xyz;
				#endif

				#if FRESNEL_PER_PIXEL
					_CALC_Fresnel_Pixel(i, worldNormal);
					#if FLIP_FRESNEL
						fresnel = 1 - fresnel;
					#endif // FLIP_FRESNEL
					i.fresnel = fresnel;
				#endif // FRESNEL_PER_PIXEL


				baseCol.xyz = lerp(tintedCol, _FresnelColor.xyz, i.fresnel);

				
				baseCol.rgb = lerp(baseCol.rgb, skyColor, i.fresnel * _Reflection);
				baseCol.rgb += specular;


				fixed specGrayscale = specular.x * 0.3 + specular.y * 0.6 + specular.z * 0.1;
				specGrayscale = saturate(specGrayscale *  5);

				baseCol.w = saturate((1-_Transparency) + i.fresnel  + specGrayscale);			
				
				//return fixed4(baseCol);

				_CACHED_SHADOW_MASKED_FRAG(i);
				_LIGHT_FROM_NORMAL_WORLD(i);
				_APPLY_SHADOW_FRAG(i);
				

                return baseCol;
            }
            ENDCG
        }
    }*/
	CustomEditor "S_GlassLayout"
}
