Shader "_Standard/S_StandardTinted_VF"
{
	SubShader
    {
    	Tags { "RenderPipeline" = "UniversalPipeline" "UniversalMaterialType" = "Lit" "RenderType" = "Opaque" }
		Offset [_OffsetLinear], [_OffsetSloped]
		Cull[_Cull]

    	Pass
    	{
	        //based on "HDRP/Lit/ShadowCaster" but with various clipping routines supported
            Name "StandardShadow"
    		Tags { "LightMode"="ShadowCaster" }

            Cull[_CullMode]

            ZClip [_ZClip]
            ZWrite On
            ZTest LEqual

            ColorMask 0

            HLSLPROGRAM

            #pragma target 4.5
            #pragma only_renderers d3d11 playstation xboxone xboxseries vulkan metal switch
            //enable GPU instancing support
            #pragma multi_compile_instancing
            #pragma instancing_options renderinglayer
            #pragma multi_compile _ DOTS_INSTANCING_ON
            // enable dithering LOD crossfade
            #pragma multi_compile _ LOD_FADE_CROSSFADE
    		#pragma multi_compile_local _ CSGCUBOID_ADDITION
			#pragma multi_compile_local _ _CIRCLECLIP
            #define SHADOWS_SHADOWMASK true
			#define VARYINGS_NEED_POSITION_WS true
            #define SHADERPASS SHADERPASS_SHADOWS

			#pragma skip_variants DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

            #include "Packages/com.unity.render-pipelines.universal/Shaders/LitInput.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/ShadowCasterPass.hlsl"
            
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_BoxClip.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_CircleClip.cginc"
            #include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_SubMove.cginc"
            
            /*#pragma vertex Vert
            #pragma fragment frag_shadow//Frag

            float4 frag_shadow(PackedVaryingsType i) : SV_Target0
            {
            	FragInputs input = UnpackVaryingsToFragInputs(i);
            	float3 posWS = GetAbsolutePositionWS(input.positionRWS);
            	Include_ScreenSpaceBooleanSubtractCuboid_FragmentWP(posWS);
            	CircleClip_Fragment(posWS);
            	return float4(1,1,1,1);
            }*/
            #pragma vertex ShadowPassVertex_Clipped
            #pragma fragment ShadowPassFragment_Clipped
			struct Varyings_Clipped
			{
			    float2 uv           : TEXCOORD0;
			    float4 positionCS   : SV_POSITION;
            	float3 positionWS   : TEXCOORD1;
			};
            Varyings_Clipped ShadowPassVertex_Clipped(Attributes input)
			{
			    Varyings_Clipped output;
			    UNITY_SETUP_INSTANCE_ID(input);

				ApplyAllSubMoves(input.positionOS);

			    output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
			    output.positionCS = GetShadowPositionHClip(input);
			    output.positionWS = TransformObjectToWorld(input.positionOS.xyz);
			    return output;
			}
			half4 ShadowPassFragment_Clipped(Varyings_Clipped input) : SV_TARGET
			{
			    Include_ScreenSpaceBooleanSubtractCuboid_FragmentWP(input.positionWS);
			    CircleClip_Fragment(input.positionWS);
			    Alpha(SampleAlbedoAlpha(input.uv, TEXTURE2D_ARGS(_BaseMap, sampler_BaseMap)).a, _BaseColor, _Cutoff);
			    return 0;
			}
            

            #include "Packages/com.unity.render-pipelines.universal/Shaders/LitInput.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/ShadowCasterPass.hlsl"

            ENDHLSL
    	}
    	
        Pass
    	
        {
			Name "StandardTinted_LOD300"
        	Tags { "LightMode"="UniversalForward" }
	 

            HLSLPROGRAM
            #pragma vertex vert_Standard
            #pragma fragment frag
			#pragma multi_compile_instancing
			#pragma multi_compile_fwdbase
			#define NORMALIZE_TANGENTS_PER_PIXEL 1
			#define STANDARD_SHADER 1

			#pragma multi_compile __ _PATTERNED
			#pragma multi_compile __ _AO_ON_UV_2
            #pragma multi_compile_fragment _ SHADOWS_SHADOWMASK
    		#pragma multi_compile_local _ CSGCUBOID_ADDITION
			#pragma multi_compile_local _ _CIRCLECLIP
			#pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE
			#pragma multi_compile_fragment _ _SHADOWS_SOFT

			#pragma skip_variants DIRECTIONAL DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON

            #include "Assets/_Art/TA/Shaders/Old/ForwardRendering/CustomLighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Functions.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_StandardTintedUtils.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_PremiumRewards.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Terrain.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Lighting.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/CustomInc_Defines_StandardVF.cginc"
			#include "Assets/_Art/TA/ShaderScripts/Resources/Libs/ShaderTemplates/CustomInc_VertShaders.cginc"

			half _EmissCutOff;
			sampler2D _Emission;

			#if _USE_SSS
				SSSDefs
			#endif

			// Add fire textures and parameters if a material supports it
			#if _IS_ON_FIRE
				FireDefs;
			#endif

            half2 ParallaxOffset1Step(half height, half amplitude, half3 viewDirTS)
            {
                height = height * amplitude - amplitude / 2.0;
                half3 v = normalize(viewDirTS);
                v.z += 0.42;
                return height * (v.xy / v.z);
            }
            
            float4 frag (v2f_Standard i, float faceDir : VFACE) : SV_Target
            {
				Include_ScreenSpaceBooleanSubtractCuboid_Fragment;
            	CircleClip_Fragment(i.worldPos);
            	CircleClip_FragmentForward(faceDir);
            	
				#if _USE_DITHER_OPACITY
					half clipVal = _BlueNoiseDither(i.screenPos, _DitherOpacity);

					clip(0 - clipVal);
				#endif

#if false
				// Parallax
				half h = tex2D(_MetallicGlossMap, i.uv).g;
				h = saturate((h - 0) * 4 + .5);
				float3 viewDirWS = normalize(_WorldSpaceCameraPos - i.worldPos.xyz);
				half3 viewDirTS = mul(viewDirWS, half3x3(i.tspace0, i.tspace1, i.tspace2));
				float2 offset = ParallaxOffset1Step(h, .08, viewDirTS);
				i.uv += offset;
				// ==
#endif

				_BASE_COL_WITH_TINT;
				DoAlphaTest_VF;

			//Normal
				half3 worldNormal = lerp(half3(0,0,1), UnpackNormal(tex2D(_BumpMap, i.uv)), _BumpScale);
#if _VERTEX_COLOUR
				_WORLD_NORMAL_FRAG_LQ(i);
#else
				_WORLD_NORMAL_FRAG(i);
#endif
				float3 viewDir = normalize(_WorldSpaceCameraPos - i.worldPos.xyz);
            	
				float3 grunge = Grunge(i.uv, i.agingUV, i.tint, _Aging, i.worldPos, baseCol.xyz, worldNormal);
				baseCol.xyz = grunge;

				_METAL_LOD300(i, viewDir);

            	
				float smoothness = coreGlossiness;
				float metallic = metalMask.r;

				baseCol.xyz = lerp(baseCol, _OwnLand(baseCol, i.worldPos), _AffectedByDistricts).xyz;

				_CACHED_SHADOW_MASKED_FRAG(i);
				_LIGHT_FROM_NORMAL_WORLD(i);
				_APPLY_SHADOW_FRAG(i);
				_BOUNCELIGHT;

				#if _PREMIUM_REWARD
					float3 iridescence = _Iridescence(i.viewDir, worldNormal);
					finalCol.xyz += _Glitter(i.uv, worldNormal, i.tint, i.premiumVars, i.viewDir, i.worldPos, float3x3(i.tspace0, i.tspace1, i.tspace2));
					finalCol.xyz = lerp(finalCol.xyz ,iridescence , i.premiumVars.y);
				#endif

				float reflectionSurfs = metalMask.w; // * metalMask.w;

				
				_ApplyDistanceFog(i.distanceFog, finalCol.rgb);
            	
				float3x3 u = UNITY_MATRIX_I_VP;
				baseCol.r += mul(u,0)*.011;

				return ApplyLighting(baseCol.rgb, worldNormal, i.worldPos, smoothness, metallic);
            }
            ENDHLSL
        }
    }
	
}
