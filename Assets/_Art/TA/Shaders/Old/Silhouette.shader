Shader "Silhouette" {
    Properties {
        _OccludedColor("Occluded Color", Color) = (0.3,0,0,1.0)
        _SrcBlend("SrcBlend", Float) = 0
        _DstBlend("DstBlend", Float) = 0
    }
    SubShader {

        Tags { "RenderType"="Opaque" "Queue"="Geometry+198" }

            ZTest Greater
            ZWrite Off
            ColorMask RGBA
            Blend [_SrcBlend] [_DstBlend]
            CGPROGRAM
            #pragma surface surf NoLight
            half4 _OccludedColor;
            struct Input { float4 color : COLOR; };
            void surf(Input IN, inout SurfaceOutput o) {}
            half4 LightingNoLight(SurfaceOutput s, UnityGI gi) { return half4(_OccludedColor.rgb, 1); }
            half4 LightingNoLight_Deferred (SurfaceOutput s, UnityGI gi, out half4 outDiffuseOcclusion, out half4 outSpecSmoothness, out half4 outNormal) {
                outDiffuseOcclusion = half4(0,0,0,0);
                outSpecSmoothness = half4(0,0,0,0);
                outNormal = half4(1,0,0,0);
                return half4(_OccludedColor.rgb, 1);
            }
            void LightingNoLight_GI (SurfaceOutput s, UnityGIInput data, inout UnityGI gi) { }
            ENDCG
    }
    FallBack "Diffuse"
}
