Shader "Unlit/InstantiatedGrass"
{
    Properties
    {
        _GrassTexture("Grass Texture", 2D) = "white" {}
        [Space][Space]
        [Header(Color)][Space]
        _GrassColorTop("Grass Tint Top", color) = (0,0,0,0)
        _GrassColorDown("Grass Tint Bottom", color) = (0,0,0,0)
        _ShadowColor("Shadow Color Multiplier", color) = (0,0,0,0)
        _SSSColor("Sun SubSurface Color", color) = (0,0,0,0)
        _FireColor("Wind Effect Tint", color) = (0,0,0,0)


        [Space][Space][Header(Miscs)][Space]
        _Radius("Radius", Range(0,7000)) = 500
        _WindIntensity("Wind Intensity", float) = 1
        _WindOrientation("Wind Orientation", vector) = (0,0,0)
    }

    SubShader
    {

        Stencil
        {
            Ref 0
            Comp lequal
        }
        Pass
        {
            Cull off
            ZWrite on
            HLSLPROGRAM
            static const float3 vertices[6] =
            {
                float3(0, 0, 0),
                float3(1, 0, 0),
                float3(0, 1, 0),
                float3(1, 0, 0),
                float3(1, 1, 0),
                float3(0, 1, 0),
            };

            static const float2 UVs[6] =
            {
                float2(0, 0),
                float2(1, 0),
                float2(0, 1),
                float2(1, 0),
                float2(1, 1),
                float2(0, 1),
            };

            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "noiseSimplex.cginc"

            StructuredBuffer<float3> _WaveParticle; // xy -> position | z -> lifeSpan

            struct appdata
            {
                float4 vertex : POSITION;
                uint vid : SV_VertexID;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                half4 colorInfo : TEXCOORD1; // x: WindIntensity  y:yMask 
                float3 normal : TEXCOORD2;
                float3 worldPos : TEXCOORD3;
                float yMask : TEXCOORD4;
                half3 color : TEXCOORD5;
            };

            #define PI 3.141592
            #define HalfPI 1.570796

            sampler2D _GrassTexture;
            sampler2D _DetailMap;
            sampler2D _HeightMap;

            float3 _WindOrientation, _SimCamPos, _HillPos;
            half4 _GrassColorTop, _GrassColorDown, _SSSColor, _ShadowColor, _FireColor;
            float _Radius, _WindIntensity;
            float4 _HeightmapDot;
            float _MinSize, _MaxSize;
            float _UseGrassTint;

            float hash11(float p)
            {
                p = frac(p * .1031);
                p *= p + 33.33;
                p *= p + p;
                return frac(p);
            }

           float _SeedOffset;
            float2 hash21(float p)
            {
                 p+= _SeedOffset;
                float3 p3 = frac(float3(p, p, p) * float3(.1031, .1030, .0973));
                p3 += dot(p3, p3.yzx + 33.33);
                return frac((p3.xx + p3.yz) * p3.zy);
            }

            float3x3 AngleAxis3x3(float angle, float3 axis)
            {
                float c, s;
                sincos(angle, s, c);

                float t = 1 - c;
                float x = axis.x;
                float y = axis.y;
                float z = axis.z;

                return float3x3(
                    t * x * x + c, t * x * y - s * z, t * x * z + s * y,
                    t * x * y + s * z, t * y * y + c, t * y * z - s * x,
                    t * x * z - s * y, t * y * z + s * x, t * z * z + c
                );
            }

            float _GrassYOffset(sampler2D _HeightMap, float4 patchUVs)
            {
                float4 heightTex = tex2Dlod(_HeightMap, patchUVs);
                float height = dot(heightTex, _HeightmapDot);
                float offset = -height;
                return offset;
            }

            float4 _GrassColoursAndHeights[16];
            float3 ComputeVertex(int idx, float instanceId, out float windIntensity, out float3 normal, out float yMask, out float3 colour)
            {
                float3 vertex = vertices[idx];
                normal = float3(0, 0, 1);

                instanceId *= 0.0005;
                const float r = hash11(instanceId);

                //Rotation
                const float3x3 rotationMatrix = AngleAxis3x3(r * PI * 2, float3(0, 1, 0));
                vertex = mul(rotationMatrix, vertex);
                normal = mul(rotationMatrix, normal);

                float2 randomPos = (hash21(instanceId) - .5) * _Radius;

                //Wind
                _WindOrientation = normalize(_WindOrientation);
                const float3 worldPos = mul(unity_ObjectToWorld, float3(randomPos.x, 0, randomPos.y));

                float4 patchUVs = float4((worldPos.x + 512) * 0.0009765625, (worldPos.z + 512) * 0.0009765625, 0, 0);
                float offset = _GrassYOffset(_HeightMap, patchUVs);

                //float dsc = tex2Dlod(_DetailMap, patchUVs);
                //if (dsc < .1) return 0;
                //GrassDetails
                const float _GrassPlacementThreshold = 1;
                const float c_uvOffs = 1.3 * .5 / 1024;
                float discardMap = tex2Dlod(_DetailMap, patchUVs + float4(-c_uvOffs, 0, 0, 0)).x * (256 / 8);
                float discardMap2 = tex2Dlod(_DetailMap, patchUVs + float4(c_uvOffs, 0, 0, 0)).x * (256 / 8);
                float discardMap3 = tex2Dlod(_DetailMap, patchUVs + float4(0, -c_uvOffs, 0, 0)).x * (256 / 8);
                float discardMap4 = tex2Dlod(_DetailMap, patchUVs + float4(0, c_uvOffs, 0, 0)).x * (256 / 8);
                int4 grassType = int4((int)(discardMap + .5) - 1, (int)(discardMap2 + .5) - 1, (int)(discardMap3 + .5) - 1, (int)(discardMap4 + .5) - 1);
                int4 isGrass = step(0, grassType);
                grassType *= isGrass;
                float4 grassDetails = _GrassColoursAndHeights[grassType.x] * isGrass.x;
                float4 grassDetails2 = _GrassColoursAndHeights[grassType.y] * isGrass.y;
                float4 grassDetails3 = _GrassColoursAndHeights[grassType.z] * isGrass.z;
                float4 grassDetails4 = _GrassColoursAndHeights[grassType.w] * isGrass.w;
                grassDetails = lerp(grassDetails, grassDetails2, step(grassDetails.a, grassDetails2.a));
                grassDetails = lerp(grassDetails, grassDetails3, step(grassDetails.a, grassDetails3.a));
                grassDetails = lerp(grassDetails, grassDetails4, step(grassDetails.a, grassDetails4.a));
                discardMap = grassDetails.a * 16;
                float yScale = discardMap * .25f;
                discardMap = max(0, 1 - discardMap);
                if (discardMap > .99) return 0;
                colour = grassDetails.xyz;
                vertex.y *= yScale;
                //GrassDetails

                float f = (_Time.x * 8);
                float noise = snoise(worldPos.xz * .003 - float2(_WindOrientation.x * f, _WindOrientation.z * f));
                noise += snoise(worldPos.xz * .001 - float2(_WindOrientation.x * f, _WindOrientation.z * f));
                noise += snoise(worldPos.xz * 2 - _WindOrientation * (_Time.x * 40)) * .2;
                noise *= _WindIntensity;

                const float3 pivot = cross(float3(0, 1, 0), abs(_WindOrientation));
                const float3x3 windRotateMatrix = AngleAxis3x3(noise * HalfPI * .3, pivot);
                
                //Scale
                float scale = lerp(_MinSize, _MaxSize, r);
                vertex *= scale * float3(.3, 1, .3);
                
                yMask = vertices[idx].y / scale;
                vertex = lerp(vertex, mul(windRotateMatrix, vertex + float3(.5, 0, 0)), vertices[idx].y);

                //Position

                vertex += float3(randomPos.x, -offset, randomPos.y);

                windIntensity = saturate(noise);
                return vertex;
            }

            v2f vert(appdata v)
            {
                v2f o;

                //Vertex Construction
                const uint triangleIndex = v.vid / 3; // triangle Index 
                const uint vertexIndex = v.vid - triangleIndex * 3; // vertex Index
                const float instanceId = v.vid / 6;
                int idx = triangleIndex % 2 == 0 ? vertexIndex : vertexIndex + 3;

                float3 normal;
                float windIntensity;
                float yMask = 0;

                float3 colour;
                v.vertex = float4(ComputeVertex(idx, instanceId, windIntensity, normal, yMask, colour), 1);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                // float dist = 1  (distance(o.worldPos, _HillPos) / _HillDistance);
                // dist = smoothstep(0, 1, dist);
                // v.vertex += float4(0, _HillSize, 0, 0) * dist;

                o.normal = normal;
                o.vertex = TransformObjectToHClip(v.vertex);
                o.colorInfo.x = windIntensity;
                o.colorInfo.yzw = colour;
                o.uv = UVs[idx];
                o.yMask = yMask;

                return o;
            }

            float4 frag(v2f i, bool IsFacing:SV_IsFrontFace) : SV_Target
            {
                half4 data = tex2D(_GrassTexture, i.uv);
                if (data.a < .5) discard;


                float3 normal = IsFacing ? i.normal : -i.normal;

                float3 lightDir = normalize(_MainLightPosition.xyz);
                float light = abs(dot(normal, lightDir));

                float3 viewDir = normalize(i.worldPos - _WorldSpaceCameraPos);

                float sss = pow(saturate(dot(viewDir, _MainLightPosition.xyz)), 5);
                // float angle = pow(saturate(dot(normal, -lightDir)), 3);
                // sss *= angle;

                float gradient = i.yMask;
                sss *= gradient;

                half4 color = _UseGrassTint ? data : lerp(_GrassColorDown, _GrassColorTop, gradient);
                color.xyz = i.colorInfo.yzw;
                color = lerp(color * _ShadowColor, color, light);
                color += _SSSColor * sss;
                color -= i.colorInfo.x * _FireColor;


                return color;
            }
            ENDHLSL
        }
        Pass
        {
            Tags
            {
                "Tag"="DepthNormals"
            }
            Cull off
            ZWrite on
            HLSLPROGRAM
            static const float3 vertices[6] =
            {
                float3(0, 0, 0),
                float3(1, 0, 0),
                float3(0, 1, 0),
                float3(1, 0, 0),
                float3(1, 1, 0),
                float3(0, 1, 0),
            };

            static const float2 UVs[6] =
            {
                float2(0, 0),
                float2(1, 0),
                float2(0, 1),
                float2(1, 0),
                float2(1, 1),
                float2(0, 1),
            };

            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            #include "noiseSimplex.cginc"

            StructuredBuffer<float3> _WaveParticle; // xy -> position | z -> lifeSpan

            struct appdata
            {
                float4 vertex : POSITION;
                uint vid : SV_VertexID;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                half4 colorInfo : TEXCOORD1; // x: WindIntensity  y:yMask 
                float3 normal : TEXCOORD2;
                float3 worldPos : TEXCOORD3;
                float yMask : TEXCOORD4;
                half3 color : TEXCOORD5;
            };

            #define PI 3.141592
            #define HalfPI 1.570796

            sampler2D _GrassTexture;
            half4 _GrassColorTop, _GrassColorDown, _SSSColor, _ShadowColor, _FireColor;
            float _Radius, _WindIntensity, _HillDistance, _HillSize;
            float3 _WindOrientation, _SimCamPos, _HillPos;

            float hash11(float p)
            {
                p = frac(p * .1031);
                p *= p + 33.33;
                p *= p + p;
                return frac(p);
            }

            float _SeedOffset;
            float2 hash21(float p)
            {
                 p+= _SeedOffset;
                float3 p3 = frac(float3(p, p, p) * float3(.1031, .1030, .0973));
                p3 += dot(p3, p3.yzx + 33.33);
                return frac((p3.xx + p3.yz) * p3.zy);
            }

            float3x3 AngleAxis3x3(float angle, float3 axis)
            {
                float c, s;
                sincos(angle, s, c);

                float t = 1 - c;
                float x = axis.x;
                float y = axis.y;
                float z = axis.z;

                return float3x3(
                    t * x * x + c, t * x * y - s * z, t * x * z + s * y,
                    t * x * y + s * z, t * y * y + c, t * y * z - s * x,
                    t * x * z - s * y, t * y * z + s * x, t * z * z + c
                );
            }

            float4 _HeightmapDot;
            sampler2D _HeightMap;
            float _MinSize, _MaxSize;
            float _UseGrassTint;
            int seedOffset;

            float _GrassYOffset(sampler2D _HeightMap, float4 patchUVs)
            {
                float4 heightTex = tex2Dlod(_HeightMap, patchUVs);
                float height = dot(heightTex, _HeightmapDot);
                float offset = -height;
                return offset;
            }

            float3 ComputeVertex(int idx, float instanceId, out float windIntensity, out float3 normal, out float3 colour)
            {
                float3 vertex = vertices[idx];
                normal = float3(0, 0, 1);
                colour = 0;

                instanceId *= 0.0005;
                const float r = hash11(instanceId);

                //Rotation
                const float3x3 rotationMatrix = AngleAxis3x3(r * PI * 2, float3(0, 1, 0));
                vertex = mul(rotationMatrix, vertex);
                normal = mul(rotationMatrix, normal);

                float2 randomPos = (hash21(instanceId) - .5) * _Radius;

                //Wind
                _WindOrientation = normalize(_WindOrientation);
                const float3 worldPos = mul(unity_ObjectToWorld, float3(randomPos.x, 0, randomPos.y));

                float4 patchUVs = float4((worldPos.x + 512) * 0.0009765625, (worldPos.z + 512) * 0.0009765625, 0, 0);
                float offset = _GrassYOffset(_HeightMap, patchUVs);

                float f = (_Time.x * 8);
                float noise = snoise(worldPos.xz * .003 - float2(_WindOrientation.x * f, _WindOrientation.z * f));
                noise += snoise(worldPos.xz * .001 - float2(_WindOrientation.x * f, _WindOrientation.z * f));
                noise += snoise(worldPos.xz * 2 - _WindOrientation * (_Time.x * 40)) * .2;
                noise *= _WindIntensity;


                //Scale
                float scale = lerp(_MinSize, _MaxSize, r);
                vertex *= scale * float3(.3, 1, .3);

                const float yMask = vertices[idx].y / scale;
                const float3 pivot = cross(float3(0, 1, 0), abs(_WindOrientation));
                const float3x3 windRotateMatrix = AngleAxis3x3(noise * HalfPI * .3, pivot);

                vertex = lerp(vertex, mul(windRotateMatrix, vertex + float3(.5, 0, 0)), vertices[idx].y);

            

                //Position

                vertex += float3(randomPos.x, -offset, randomPos.y);

                windIntensity = saturate(noise);
                return vertex;
            }

            v2f vert(appdata v)
            {
                v2f o;

                //Vertex Construction
                const uint triangleIndex = v.vid / 3; // triangle Index 
                const uint vertexIndex = v.vid - triangleIndex * 3; // vertex Index
                const float instanceId = v.vid / 6;
                int idx = triangleIndex % 2 == 0 ? vertexIndex : vertexIndex + 3;

                float3 normal;
                float windIntensity;

                float3 colour;
                v.vertex = float4(ComputeVertex(idx, instanceId, windIntensity, normal, colour), 1);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                // float dist = 1  (distance(o.worldPos, _HillPos) / _HillDistance);
                // dist = smoothstep(0, 1, dist);
                // v.vertex += float4(0, _HillSize, 0, 0) * dist;

                o.normal = normal;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.colorInfo.x = windIntensity;
                o.colorInfo.yzw = colour.xyz;
                o.uv = UVs[idx];

                return o;
            }

            float4 frag(v2f i, bool IsFacing:SV_IsFrontFace) : SV_Target
            {
                return 1;
            }
            ENDHLSL
        }

    }
}