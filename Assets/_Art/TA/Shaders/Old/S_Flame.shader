Shader "Custom/Flame"
{
	Properties
	{
		_MainTex ("Texture", 2D) = "white" {}
		_FlameGrad ("Flame gradient", 2D) = "white" {}
		_NormMap ("Normal Map", 2D) = "bump" {}
		_BumpIntensity ("Bump intensity", float) = 1
		_BumpMapScale ("Bump scale", float) = 1
		_Hue ("Hue", float) = 0
		_Saturation ("Saturation", float) = 1
		_Value ("Value", float) = 1
		_Emission ("Emissive brightness", float) = 0
		_LightBlockingScale ("Light blocking scale", float) = 1
		_LightBlocking ("Light blocking", float) = 1
		_MeshTop ("Mesh max Y", float) = 1
		_MeshBottom ("Mesh min Y", float) = -1
		_FlickerSpeed ("Flicker speed", float) = 100
		_Flicker ("Flicker", Vector) = (0, 1, 0, 0)
		_UnscaledTime ("Unscaled Time", float) = 0.0
	}
	SubShader
	{
		Tags { "RenderType"="Transparent" "Queue"="Transparent" }
		Blend SrcAlpha OneMinusSrcAlpha 
		LOD 100

		Pass
		{
			HLSLPROGRAM
			#pragma vertex vert:alpha
			#pragma fragment frag
			#pragma NoLighting noambient noshadow novertexlights nolightmap noforwardadd nometa

			#include "UnityStandardUtils.cginc"
			#include "UnityCG.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
				float3 normal : NORMAL;
				float4 tangent : TANGENT;
			};

			struct v2f
			{
				float4 uv : TEXCOORD0;
				float4 vertex : SV_POSITION;
				float3 worldPos : WORLDPOS;
				float3 normal : NORMAL;
				float4 tangent : TANGENT;
				float yLoc : HEIGHT;
			};

			sampler2D _MainTex;
			sampler2D _NormMap;
			sampler2D _FlameGrad;
			float4 _MainTex_ST;
			float4 _NormMap_ST;

			float _BumpIntensity;
			float _BumpMapScale;
			float _Hue;
			float _Saturation;
			float _Emission;
			float _Value;
			float _Contrast;
			float _Brightness;
			float _LightBlockingScale;
			float _LightBlocking;
			float _MeshTop;
			float _MeshBottom;
			float _FlickerSpeed;
			float4 _Flicker;
			float _UnscaledTime;

			float3 hsv_to_rgb(float3 hsv) 
			{
				float h = 6.0f * frac(hsv.x);

				float3 hue = saturate(float3(
					abs(h-3.0f) - 1.0f,
					2.0f - abs(h - 2.0f),
					2.0f - abs(h - 4.0f)));

				return ((hue - 1.0f) * hsv.y + 1.0f) * hsv.z;
			}

			const float EPSILON = 1e-10;
 
			float3 rgb_to_hsv(float3 rgb)
			{
				float4 p = (rgb.g < rgb.b) ? float4(rgb.bg, -1.0, 2.0/3.0) : float4(rgb.gb, 0.0, -1.0/3.0);
				float4 q = (rgb.r < p.x) ? float4(p.xyw, rgb.r) : float4(rgb.r, p.yzx);
				float c = q.x - min(q.w, q.y);
				float h = abs((q.w - q.y) / (6 * c + EPSILON) + q.z);
				return float3(h, c / (q.x + EPSILON), q.x);
			}

			float4 colFromFade(float fade, float height, float blocking)
			{
				float timeDiff = _UnscaledTime - _Flicker.x;
				float flicker = _Flicker.y / (max(0.1, _FlickerSpeed * timeDiff * timeDiff));

				float2 offset = float2(-blocking * 0.5 - flicker * 0.1, blocking);
				float4 col = tex2D(_FlameGrad, float2(lerp(fade, 0.7, 0.5 * flicker + blocking), height * height * (2 - flicker)) + offset);

				float3 hsv = rgb_to_hsv(col.xyz);
				hsv.x += _Hue;
				hsv.y *= _Saturation;
				hsv.z = hsv.z * _Value + _Emission;
				col.xyz = hsv_to_rgb(hsv);
				return col;
			}

			v2f vert (appdata v)
			{
				v2f o;
				o.uv.xy = TRANSFORM_TEX(v.uv, _MainTex);
				o.uv.zw = TRANSFORM_TEX(v.uv, _NormMap);

				float fade = dot(v.normal.xz, normalize((_WorldSpaceCameraPos - mul(unity_ObjectToWorld, float4(v.vertex.xyz, 1))).xz));
				float3 nrmBump = UnpackNormal(tex2Dlod(_NormMap, float4(v.uv * _BumpMapScale, 0, 0)));
				nrmBump.xy *= _BumpIntensity;
				nrmBump = normalize(nrmBump);
				float3 nrm = UnityObjectToWorldNormal(v.normal);
				float4 tan = float4(UnityObjectToWorldDir(v.tangent.xyz), v.tangent.w);
				float3 btan = cross(nrm, tan.xyz) * tan.w * unity_WorldTransformParams.w;
				o.normal = normalize(
				nrmBump.x * tan +
				nrmBump.y * btan +
				nrmBump.z * nrm);

				float3 pos = v.vertex.xyz;
				float height = pos.y;
				o.worldPos = mul(unity_ObjectToWorld, float4(pos, 1));
				o.vertex = UnityObjectToClipPos(pos);
				o.yLoc = (height - _MeshBottom) / (_MeshTop - _MeshBottom);
				return o;
			}

			float4 frag (v2f i) : SV_Target
			{
				float3 nrmBump = normalize(UnpackNormal(tex2D(_NormMap, i.uv.zw * _LightBlockingScale)));				
				float blocking = 1 - nrmBump.z * nrmBump.z;
				float4 col = tex2D(_MainTex, i.uv.xy);
				//float fade = dot(i.normal, normalize(_WorldSpaceCameraPos - i.worldPos));
				float fadeXZ = dot(i.normal.xz, normalize((_WorldSpaceCameraPos - i.worldPos).xz));
				//i.normal.y = abs(i.normal.y) / 2;
				//float fadeClampY = dot(i.normal, normalize(_WorldSpaceCameraPos - i.worldPos));
				return col * colFromFade(fadeXZ, i.yLoc, blocking * _LightBlocking);
			}
			ENDHLSL
		}
	}
}
