Shader "Unlit/SimpleColouredCircle"
{
    Properties
    {
        _Color("Color", Color) = (1.0,1.0,0,1.0)
        _Radius("Radius", Float) = 0.3
    }
    SubShader
    {
        Tags {"Queue"="Transparent" "RenderType"="Transparent" }// "ForceNoShadowCasting"="true" "DisableBatching"="LODFading"}
        
        LOD 100
        Blend SrcAlpha OneMinusSrcAlpha
        Pass
        {
            CGPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc" 

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float4 position : TEXCOORD1; 
                float2 uv: TEXCOORD0;
            };
            
            v2f vert (appdata_base v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.position = v.vertex;
                o.uv = v.texcoord;
                return o;
            }
           
            fixed4 _Color;
            float _Radius;
            
            float circle(float2 pt, float2 center, float radius, float edge_thickness){
                float2 p = pt - center;
                float len = length(p);
                float result = 1.0-smoothstep(radius-edge_thickness, radius, len);

                return result;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                float c = circle(i.uv, float2(.5,.5), _Radius * .5, 0.002);
                fixed3 rgb = _Color.rgb * c;
                int aTrunc = c;
                return fixed4(rgb, aTrunc * _Color.a);  
            }
            ENDCG
        }
    }
}
