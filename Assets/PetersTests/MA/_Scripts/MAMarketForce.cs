using System;
using UnityEngine;
using System.Collections.Generic;

[Serializable]
public class MAMarketForce
{
    public enum ConditionType 
    {
        MostUsedPart,
        LeastUsedPart,
        NeverUsedPart,
        OtherPart,
    }
    
    public enum EffectType
    {
        SalesPrice,
        BlockCost,
    }
    public ConditionType Condition { get; private set; }
    public EffectType Effect { get; private set; }
    
    public string m_groupName;
    public string m_condition;
    public string m_effect;
    public float m_decreaseChance;
    public float m_increaseChance;
    public float m_decreaseMin;
    public float m_decreaseMax;
    public float m_increaseMin;
    public float m_increaseMax;
    
    public static List<MAMarketForce> s_allForces = new List<MAMarketForce>();
    public static Dictionary<string, List<MAMarketForce>> s_forcesLookup = new();


    public static bool PostImportARecord(MAMarketForce _what)
    {
        _what.Condition = (ConditionType)Enum.Parse(typeof(ConditionType), _what.m_condition);
        _what.Effect = (EffectType)Enum.Parse(typeof(EffectType), _what.m_effect);
        
        // % in knack
        _what.m_decreaseChance /= 100f;
        _what.m_increaseChance /= 100f;
        _what.m_increaseMin /= 100f;
        _what.m_increaseMax /= 100f;
        _what.m_decreaseMin /= 100f;
        _what.m_decreaseMax /= 100f;
        
        return true;
    }
    
    public static List<MAMarketForce> Get(string _groupName, EffectType _effectType)
    {
        s_forcesLookup.TryGetValue($"{_groupName}:{_effectType.ToString()}", out var result);
        return result;
    }
        
    public static List<MAMarketForce> LoadInfo()
    {
        s_allForces = NGKnack.ImportKnackInto<MAMarketForce>(PostImportARecord);
     
        s_forcesLookup.Clear();   
        foreach(var mf in s_allForces)
        {
            string uid = $"{mf.m_groupName}:{mf.m_effect}";
            
            if(s_forcesLookup.TryGetValue(uid, out var result) == false)
                s_forcesLookup[uid] = new List<MAMarketForce>() { mf };
            else
                result.Add(mf);
        }
        
        return s_allForces;
    }
}