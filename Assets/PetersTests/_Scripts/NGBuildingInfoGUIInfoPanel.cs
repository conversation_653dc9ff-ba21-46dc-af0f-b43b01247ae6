using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGBuildingInfoGUIInfoPanel : NGBuildingInfoGUIComponentBase
{
    public TMP_Text m_description;
    public TMP_Text m_dateBuilt;
    public TMP_Text m_whoBuilt;
    public Image m_buildingImage;

    override public void Activate(NGCommanderBase _building)
    {/*
        base.Activate(_building);
        var info = NGBuildingInfoManager.NGBuildingInfo.GetBuildingInfo(m_building.Name);
        m_description.text = info?.m_description ?? m_building.Name;
        if (m_building is MABuilding ma)
        {
            m_description.text = ma.GetDebugInfo();
            m_description.rectTransform.sizeDelta = m_description.GetPreferredValues();
            m_description.lineSpacing = -30;
        }
        GameManager.Me.GetDesignSprite(m_building.Design, CaptureObjectImage.Use.Building, (_s) =>
        {
            var designSprite = _s;
            if (designSprite == null)
            {
                m_buildingImage.sprite = SpriteAtlasMapLoader.LoadClonedSprite("_GUI/_Default_Images/todo");
            }
            else
            {
                m_buildingImage.sprite = _s;
            }
        });
        long builtTime = _building.m_stateData.m_builtTime;
        if (builtTime == 0) builtTime = 637530048000000000L; // 3/Apr/21
        m_dateBuilt.text = DateTime.FromBinary(builtTime).ToString("d MMMM yyyy");
        m_whoBuilt.text = string.IsNullOrEmpty(_building.m_stateData.m_designedBy) ? "Peter Molyneux" : _building.m_stateData.m_designedBy;
        //NGTutorialManager.Me.m_buildingInfoGUIInfoPanelOpened = true;
*/
    }
}
