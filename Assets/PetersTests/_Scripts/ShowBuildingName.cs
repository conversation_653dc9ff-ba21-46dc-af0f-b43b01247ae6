using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class ShowBuildingName : MonoBehaviour
{
    public TMP_Text m_text;
    private NGCommanderBase m_building;
    public GameObject m_visuals;
    
    void Update()
    {
        UpdateInfo();
    }
    void Activate(NGCommanderBase _building)
    {
        m_building = _building;
        UpdateInfo();
    }
    void UpdateInfo()
    {
        var targetWorldPos = m_building.m_balloonHolder != null ? m_building.m_balloonHolder.transform.position : m_building.transform.position;
        var pos = Camera.main.WorldToScreenPoint(new Vector3(targetWorldPos.x, m_building.LocalHighestPoint.y, targetWorldPos.z));
        
        transform.position = pos;
        return;
        //var info = m_building.BuildingInfo;
        //if (info == null) return;
        //m_text.text = $"{info.m_title}";
        
        bool inView = pos.z >= 0;
        if(m_visuals != null && m_visuals.activeSelf != inView)
        {
            m_visuals.SetActive(inView);
        } 
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public static ShowBuildingName Create(NGCommanderBase _building)
    {
        var go = Instantiate(NGManager.Me.m_showBuildingNamePrefab, NGManager.Me.m_centreScreenHolder);
        var sbn = go.GetComponent<ShowBuildingName>();
        sbn.Activate(_building);
        return sbn;
    }
}
