using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class ServerConfiguration
{
    public static List<ServerConfiguration> s_serverConfigurations = new(); 
    public static List<ServerConfiguration> GetList => s_serverConfigurations;
    public string DebugDisplayName => m_key;
    public string id;
    public bool m_debugChanged;
    public string m_key;
    public string m_value;

    public static bool PostImportARecord(ServerConfiguration _what)
    {
        return true;
    }
    public static List<ServerConfiguration> LoadInfo()
    {
        s_serverConfigurations = NGKnack.ImportKnackInto<ServerConfiguration>(PostImportARecord);
        return s_serverConfigurations;
    }

}
