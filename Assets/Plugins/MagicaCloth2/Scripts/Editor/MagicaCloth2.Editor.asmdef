{"name": "MagicaClothV2.Editor", "rootNamespace": "", "references": ["Unity.Mathematics", "Unity.Collections", "Unity.Burst", "MagicaClothV2"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["MC2_BURST", "MC2_COLLECTIONS"], "versionDefines": [{"name": "com.unity.burst", "expression": "1.8.1", "define": "MC2_BURST"}, {"name": "com.unity.collections", "expression": "1.4.0", "define": "MC2_COLLECTIONS"}], "noEngineReferences": false}