[{"id": "61127adac104d90bb23fa8ea", "m_prefabName": "<PERSON>_<PERSON><PERSON>_Bottom_Bagel", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Projectile", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "1.00", "m_workerTimeToMake": "11.0", "m_numTapsToMake": "6.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.60", "m_tableScale": "1.50", "m_prefabPath": "_CurrentlyUsed/_Burgers/Burger_Bun_Bottom_Bagel", "m_displayName": "<PERSON><PERSON>", "m_description": "There's a Viennese story about the invention of the bagel (originally known as the obwarzanek). A baker made it as a tribute to the King of Poland, <PERSON>, who saved Austria from the Turkish invaders. The king loved horses, so the baker shaped the dough into a circle and called it a beugel, which is Austrian for 'stirrup'. The story is completely false, of course.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.48000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.00216", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "672e1d0047cdf302d345ed1d", "m_prefabName": "Factory_Input", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Stock|Dispatch Cotton:Stock|Dispatch Metal:Stock|Dispatch:Stock|Dispatch Swamp :Stock|Factory:Stock|Lumber Mill:Stock|Metal Smelter:Stock|Mill Produce:Stock|Shop:Stock|Weaponsmith:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "0.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_Factory/Factory_Input", "m_displayName": "StockIn", "m_description": "Receives all processed materials ready for use in the factory. ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "StockIn", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.20000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "672e1cf394669102ed772ee2", "m_prefabName": "Factory_Output", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Stock|Farm:Stock|Lumber Mill:Stock|Metal Mine:Stock|Metal Smelter:Stock|Mill Produce:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_Factory/Factory_Output", "m_displayName": "StockOut", "m_description": "When your product is finished, it will appear on here. Unless your workers are so fast they're bursting through the door and powering towards the dispatch like a monkey with its arse on fire.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|StockOut", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.20000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "613b4fdb4302e607525e026f", "m_prefabName": "InfoBoard", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "14.0", "m_numTapsToMake": "7.0", "m_materialCost": "7.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 2, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_Civic/Civic_InfoBoard", "m_displayName": "InfoBoard", "m_description": "Tracks the Market Capacity levels of your factories. You can remove it, but you'll not know how many products you can make before needing to redesign without it.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.00020", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65f1e1e457c2fe0026e5edc7", "m_prefabName": "InfoBoardOrder", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "14.0", "m_numTapsToMake": "1.0", "m_materialCost": "7.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 2, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_Civic/Civic_OrderBoard", "m_displayName": "Order Information Board", "m_description": "Handy if you need to see what your factory is up to at a glance. Useless as a pair of trousers.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "GUIOrderInfo", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.00020", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab55e6c061002f48c3fa0", "m_prefabName": "MA_Armour_BarbarianSet_Arms", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "40.00", "m_price": "16.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Arms", "m_displayName": "MA_Armour_BarbarianSet_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab8ee1e23c202c5c72e44", "m_prefabName": "MA_Armour_BarbarianSet_Chest", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "42.00", "m_price": "16.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Chest", "m_displayName": "MA_Armour_BarbarianSet_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab93b769616031656ebc0", "m_prefabName": "MA_Armour_BarbarianSet_Feet", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "44.00", "m_price": "17.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Feet", "m_displayName": "MA_Armour_BarbarianSet_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab97e769616031656ecbe", "m_prefabName": "MA_Armour_BarbarianSet_Hands", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "46.00", "m_price": "18.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Hands", "m_displayName": "MA_Armour_BarbarianSet_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab9a6c7e27b02f1bd9e3f", "m_prefabName": "MA_Armour_BarbarianSet_Head", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "48.00", "m_price": "19.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Head", "m_displayName": "MA_Armour_BarbarianSet_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eab9d4769616031656ed21", "m_prefabName": "MA_Armour_BarbarianSet_Hips", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "25.00", "m_price": "10.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Hips", "m_displayName": "MA_Armour_BarbarianSet_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66eaba0c769616031656eebb", "m_prefabName": "MA_Armour_BarbarianSet_Legs", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "39.00", "m_price": "15.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_BarbarianSet_Legs", "m_displayName": "MA_Armour_BarbarianSet_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c1ece2080b02b737195b", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Arms", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "54.00", "m_price": "21.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Arms", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c1f81c116b031ba859d4", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON>eap<PERSON><PERSON><PERSON>_Chest", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "56.00", "m_price": "22.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Chest", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON>eap<PERSON><PERSON><PERSON>_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c2061c116b031ba859f5", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Feet", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "58.00", "m_price": "23.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Feet", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "2.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c20ef9c28a02c1979c93", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Hands", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "60.00", "m_price": "24.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Hands", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c2180fc49b02f290694e", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Head", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "31.50", "m_price": "12.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Head", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c225f9c28a02c1979d0a", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Hips", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "33.00", "m_price": "13.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Hips", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db5639d9b2f0301e48b7f", "m_prefabName": "<PERSON>_<PERSON><PERSON>_CheapLeather_LegPad", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "34.50", "m_price": "13.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_LegPad", "m_displayName": "<PERSON>_<PERSON><PERSON>_CheapLeather_LegPad", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6718c22ff9c28a02c1979d16", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>p<PERSON><PERSON><PERSON>_Legs", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "36.00", "m_price": "14.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapLeather_Legs", "m_displayName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>p<PERSON><PERSON><PERSON>_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc42f9", "m_prefabName": "MA_Armour_CheapPadded_Arms", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "90.00", "m_price": "36.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Arms", "m_displayName": "MA_Armour_CheapPadded_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc4300", "m_prefabName": "MA_Armour_CheapPadded_Chest", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "62.00", "m_price": "24.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Chest", "m_displayName": "MA_Armour_CheapPadded_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc4307", "m_prefabName": "MA_Armour_CheapPadded_Feet", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "96.00", "m_price": "38.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Feet", "m_displayName": "MA_Armour_CheapPadded_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc430e", "m_prefabName": "MA_Armour_CheapPadded_Hands", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "49.50", "m_price": "19.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Hands", "m_displayName": "MA_Armour_CheapPadded_Hands", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc4315", "m_prefabName": "MA_Armour_CheapPadded_Head", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "68.00", "m_price": "27.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Head", "m_displayName": "MA_Armour_CheapPadded_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45bd332b2f02cddc431c", "m_prefabName": "MA_Armour_CheapPadded_Hips", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "70.00", "m_price": "28.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Hips", "m_displayName": "MA_Armour_CheapPadded_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4323", "m_prefabName": "MA_Armour_CheapPadded_Legs", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "144.00", "m_price": "57.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_CheapPadded_Legs", "m_displayName": "MA_Armour_CheapPadded_Legs", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb2b02fc7b1027c2792c9", "m_prefabName": "MA_Armourer_Door", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_Door", "m_displayName": "Armoury Door", "m_description": "Leave your weapons outside. Then come in and buy some. You don't have to immediately throw them out the door, mind. These rules are weird, right?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb2c9b20982028890b1d0", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ney", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_Roof_Chimney", "m_displayName": "Armoury Roof & chugging chimney", "m_description": "Put it on top, the rain it will stop. Put it on side, from the rain you can't hide.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb2e1fee4b3028111e260", "m_prefabName": "MA_Arm<PERSON>er_RoofSide", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_RoofSide", "m_displayName": "Armoury Slanted Roof", "m_description": "The direction a builder sets their slant tells you all you need to know about how they hold a sword.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb33153e7cc0279823e3e", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>W<PERSON>ow", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_RoofWindow", "m_displayName": "Armoury Roof & Loft Window", "m_description": "A cute window. Don't look inside, you might see a muscular hero trying out some new loincloths. They really hate it when you watch them tottering about, trying to get that second leg in.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb34667f653028220d118", "m_prefabName": "<PERSON>_<PERSON><PERSON>er_Shade", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_Shade", "m_displayName": "Armoury Display Stall", "m_description": "Business in the front, party in the back. If, in your head, a party involves dressing up in really heavy clothes and punching things.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionArmourer", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb35d82bd85027fa7d9fa", "m_prefabName": "MA_Armourer_Sign", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_Sign", "m_displayName": "Armoury Sign", "m_description": "Some people are dumb. They'll look at your shop, see all the shields and helmets, then see no sign, and wonder what you're selling. Don't let a dumb customer get away, they're made out of money!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66cdb3757d21c20284922dd4", "m_prefabName": "MA_Armourer_Window", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Armourer:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Armourer/MA_Armourer_Window", "m_displayName": "Armoury Window", "m_description": "Look inside, and what do you see? A thousand steel skulls, staring straight back at me.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc432a", "m_prefabName": "MA_Armour_GoldPadded_Arms", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "400.00", "m_price": "160.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Arms", "m_displayName": "MA_Armour_GoldPadded_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "8.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4331", "m_prefabName": "MA_Armour_GoldPadded_Chest", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "255.00", "m_price": "102.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Chest", "m_displayName": "MA_Armour_GoldPadded_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "10.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4338", "m_prefabName": "MA_Armour_GoldPadded_Feet", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "312.00", "m_price": "124.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Feet", "m_displayName": "MA_Armour_GoldPadded_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490fe0ff2f902ac8b4412", "m_prefabName": "MA_Armour_GoldPadded_Female_Arms", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "400.00", "m_price": "160.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Arms", "m_displayName": "MA_Armour_GoldPadded_Female_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "8.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490fe0ff2f902ac8b4419", "m_prefabName": "MA_Armour_GoldPadded_Female_Chest", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "255.00", "m_price": "102.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Chest", "m_displayName": "MA_Armour_GoldPadded_Female_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "10.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490ff0ff2f902ac8b4420", "m_prefabName": "MA_Armour_GoldPadded_Female_Feet", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "312.00", "m_price": "124.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Feet", "m_displayName": "MA_Armour_GoldPadded_Female_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490ff0ff2f902ac8b4427", "m_prefabName": "MA_Armour_GoldPadded_Female_Hands", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "318.00", "m_price": "127.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Hands", "m_displayName": "MA_Armour_GoldPadded_Female_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490ff0ff2f902ac8b442e", "m_prefabName": "MA_Armour_GoldPadded_Female_Head", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "270.00", "m_price": "108.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Head", "m_displayName": "MA_Armour_GoldPadded_Female_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "10.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490ff0ff2f902ac8b4435", "m_prefabName": "MA_Armour_GoldPadded_Female_Hips", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "220.00", "m_price": "88.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Hips", "m_displayName": "MA_Armour_GoldPadded_Female_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "8.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "681490ff0ff2f902ac8b443c", "m_prefabName": "MA_Armour_GoldPadded_Female_Legs", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "196.00", "m_price": "78.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Female_Legs", "m_displayName": "MA_Armour_GoldPadded_Female_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "7.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc433f", "m_prefabName": "MA_Armour_GoldPadded_Hands", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "318.00", "m_price": "127.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Hands", "m_displayName": "MA_Armour_GoldPadded_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4346", "m_prefabName": "MA_Armour_GoldPadded_Head", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "270.00", "m_price": "108.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Head", "m_displayName": "MA_Armour_GoldPadded_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "10.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc434d", "m_prefabName": "MA_Armour_GoldPadded_Hips", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "220.00", "m_price": "88.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Hips", "m_displayName": "MA_Armour_GoldPadded_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "8.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4354", "m_prefabName": "MA_Armour_GoldPadded_Legs", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "196.00", "m_price": "78.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_GoldPadded_Legs", "m_displayName": "MA_Armour_GoldPadded_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "7.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "670644673e2abb02e8a7b0ad", "m_prefabName": "MA_Armour_LeatherGL_Arms", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "168.00", "m_price": "67.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Arms", "m_displayName": "MA_Armour_LeatherGL_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "670644710c6f6102ce47b801", "m_prefabName": "MA_Armour_LeatherGL_Chest", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "172.00", "m_price": "68.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Chest", "m_displayName": "MA_Armour_LeatherGL_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "8.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6706447c0c6f6102ce47b80e", "m_prefabName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Feet", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "176.00", "m_price": "70.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Feet", "m_displayName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67064487b6384b031ccd766d", "m_prefabName": "<PERSON>_<PERSON>our_LeatherGL_Hands", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "180.00", "m_price": "72.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Hands", "m_displayName": "<PERSON>_<PERSON>our_LeatherGL_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6706448f0c6f6102ce47b858", "m_prefabName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Head", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "230.00", "m_price": "92.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Head", "m_displayName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "10.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67064498d2243c02eaa84bdd", "m_prefabName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Hips", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "141.00", "m_price": "56.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Hips", "m_displayName": "<PERSON>_<PERSON><PERSON>_LeatherGL_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db6899d9b2f0301e494a0", "m_prefabName": "MA_Armour_LeatherGL_LegPad", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "123.00", "m_price": "49.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_LegPad", "m_displayName": "MA_Armour_LeatherGL_LegPad", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "670644a0d9be8102c12d0e8d", "m_prefabName": "MA_<PERSON>our_LeatherGL_Legs", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "126.00", "m_price": "50.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_LeatherGL_Legs", "m_displayName": "MA_<PERSON>our_LeatherGL_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66c36b92f35d7402869aa4dd", "m_prefabName": "<PERSON>_<PERSON><PERSON>_Mannequin", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "0.0", "m_numTapsToMake": "0.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_Mannequin", "m_displayName": "<PERSON>_<PERSON><PERSON>_Mannequin", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.01000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6814d007df470e02d68c5c68", "m_prefabName": "<PERSON>_<PERSON><PERSON>_Mannequin_Female", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "0.0", "m_numTapsToMake": "0.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_Mannequin_Female", "m_displayName": "<PERSON>_<PERSON><PERSON>_Mannequin_Female", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.01000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db75f2cde570302be2c45", "m_prefabName": "MA_Armour_StandardLeather_Arms", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "150.00", "m_price": "60.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Arms", "m_displayName": "MA_Armour_StandardLeather_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "5.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db769891fb602e3f52960", "m_prefabName": "MA_Armour_StandardLeather_Chest", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "93.00", "m_price": "37.20", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Chest", "m_displayName": "MA_Armour_StandardLeather_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db7711550e702bde9fb5b", "m_prefabName": "MA_Armour_StandardLeather_Feet", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "160.00", "m_price": "64.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Feet", "m_displayName": "MA_Armour_StandardLeather_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "5.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db77a1550e702bde9fc2c", "m_prefabName": "MA_Armour_StandardLeather_Hands", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "132.00", "m_price": "52.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Hands", "m_displayName": "MA_Armour_StandardLeather_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db783346c9a02becd7d12", "m_prefabName": "MA_Armour_StandardLeather_Head", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "85.00", "m_price": "34.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Head", "m_displayName": "MA_Armour_StandardLeather_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "5.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db78b2cde570302be39d3", "m_prefabName": "MA_Armour_StandardLeather_Hips", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "52.50", "m_price": "21.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Hips", "m_displayName": "MA_Armour_StandardLeather_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db7a22cde570302be3ac3", "m_prefabName": "MA_Armour_StandardLeather_LegPad", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "72.00", "m_price": "28.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_LegPad", "m_displayName": "MA_Armour_StandardLeather_LegPad", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "674db7972cde570302be3a60", "m_prefabName": "MA_Armour_StandardLeather_Legs", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "74.00", "m_price": "29.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardLeather_Legs", "m_displayName": "MA_Armour_StandardLeather_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc435b", "m_prefabName": "MA_Armour_StandardPadded_Arms", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "124.00", "m_price": "49.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Arms", "m_displayName": "MA_Armour_StandardPadded_Arms", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4362", "m_prefabName": "MA_Armour_StandardPadded_Chest", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Chest", "m_sellingPrice": "96.00", "m_price": "38.40", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Chest", "m_displayName": "MA_Armour_StandardPadded_Chest", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "6.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4369", "m_prefabName": "MA_Armour_StandardPadded_Feet", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Feet", "m_sellingPrice": "132.00", "m_price": "52.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Feet", "m_displayName": "MA_Armour_StandardPadded_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4370", "m_prefabName": "MA_Armour_StandardPadded_Hands", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Arms", "m_sellingPrice": "102.00", "m_price": "40.80", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Hands", "m_displayName": "MA_Armour_StandardPadded_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4377", "m_prefabName": "MA_Armour_StandardPadded_Head", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Head", "m_sellingPrice": "70.00", "m_price": "28.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Head", "m_displayName": "MA_Armour_StandardPadded_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "4.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc437e", "m_prefabName": "MA_Armour_StandardPadded_Hips", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Hips", "m_sellingPrice": "46.50", "m_price": "18.60", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Hips", "m_displayName": "MA_Armour_StandardPadded_Hips", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "3.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678a45be332b2f02cddc4385", "m_prefabName": "MA_Armour_StandardPadded_Legs", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Armour", "m_mADrawerInfos": "Armour:Legs", "m_sellingPrice": "80.00", "m_price": "32.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Armour/MA_Armour_StandardPadded_Legs", "m_displayName": "MA_Armour_StandardPadded_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "5.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67bdc7c005ef1d030cdcadaa", "m_prefabName": "MABase1x1Beacon", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Base/MABase1x1Beacon", "m_displayName": "MABeaconBase1x1", "m_description": "Beacon placeholder\n", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon|Tap", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "68415e4e7231bb02d64802c3", "m_prefabName": "MA_Beacon_1", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Beacon :Beacon Parts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Beacon/MA_Beacon_1", "m_displayName": "Beacon Base (1)", "m_description": "base", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": "True"}, {"id": "68415e6469c1b102f3f2aeb5", "m_prefabName": "MA_Beacon_2", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Beacon :Beacon Parts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Beacon/MA_Beacon_2", "m_displayName": "Beacon 2", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": "True"}, {"id": "68415e717231bb02d648035d", "m_prefabName": "MA_Beacon_3", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Beacon :Beacon Parts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Beacon/MA_Beacon_3", "m_displayName": "Beacon 3", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": "True"}, {"id": "68415e825094c002cf002894", "m_prefabName": "MA_Beacon_4", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Beacon :Beacon Parts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Beacon/MA_Beacon_4", "m_displayName": "Beacon 4", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": "True"}, {"id": "68415e917231bb02d64803ce", "m_prefabName": "MA_Beacon_5", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Beacon :Beacon Parts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Beacon/MA_Beacon_5", "m_displayName": "Beacon Dome (5)", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.01000", "m_attack": "0.15000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": "True"}, {"id": "664767c3cde43b002669e241", "m_prefabName": "MA_BowlA", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "8.20", "m_price": "4.54", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_BowlA", "m_displayName": "Soup Bowl", "m_description": "The finest ceramics money can buy. You'll be bowled over by how souper this bowl is. (We've taken a £1 off the asking price for this bowl due to the dreadful nature of the previous puns)", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.12100", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653a2a593f2b9a002880d1d1", "m_prefabName": "MA_Bread_BreadBowl", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "4.60", "m_price": "2.78", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_BreadBowl", "m_displayName": "Crusty Bread Bowl", "m_description": "Too poor for china wear? Too tight to waste money on fripperies like plates and bowls? Bread bowl! Two birds, one stone, no costs!", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.07400", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc46546d667002750cecf", "m_prefabName": "MA_Bread_BreadBowlSoup", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "4.20", "m_price": "2.36", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_BreadBowlSoup", "m_displayName": "Filled Bread Bowl", "m_description": "For the mean spirited, this filled Bread Bowl will feed them <i>and</i> spare them from the drudgery of washing up. You can't lose! What's in the filling? Don't ask difficult questions…", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06300", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc4bc28eb2a00284913fa", "m_prefabName": "MA_Bread_CheapLoaf", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "3.50", "m_price": "2.66", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_CheapLoaf", "m_displayName": "Stale Loaf", "m_description": "If it ain't mouldy, it ain't off limits. Gorge yourself on this cheaper than chips crust. You won't get fat because there's nowt left in it to balloon with. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.07100", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc4cb46d667002750cfaa", "m_prefabName": "MA_Bread_CottageLoaf", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "4.40", "m_price": "2.55", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_CottageLoaf", "m_displayName": "Cob Loaf", "m_description": "These dual purpose breads are a homemaker's best friend. Eat it while it's fresh and then, when it's gone bad, you can use it as a cushion for the grapes you'll get stuffing a whole one of these down your dirty great maw.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06800", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc4d2f4cbb70027a2f607", "m_prefabName": "MA_Bread_LoafOfBread", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "4.30", "m_price": "2.51", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_LoafOfBread", "m_displayName": "Half Loaf", "m_description": "The champion's favourite! Champions of living alone, that is. No point wasting good money on a whole loaf when you're all alone in your little hovel. You only look half as sad in there with half a loaf on your table.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06700", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc4da609c8b0028ff0b00", "m_prefabName": "MA_Bread_LongLoaf", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "4.10", "m_price": "2.40", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.20", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_LongLoaf", "m_displayName": "Baguette", "m_description": "Rustic and versatile. This continental crust makes a fine accompaniment to many a stew and casserole. Plus, once stale, it doubles up as a fine baton for crowd control.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06400", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc4e33578c2002a32b2cb", "m_prefabName": "MA_Bread_SliceOfBread", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "2.10", "m_price": "0.38", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Bread/MA_Bread_SliceOfBread", "m_displayName": "Solitary Slice", "m_description": "<PERSON> at the amazing sliced bread! Save time at home with this convenient cut. Stylish and modern, this is bread for people on the go, people with a bright future, people with ambition!", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.03000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67cf139389c8e00314b52226", "m_prefabName": "MA_ChickenCoop", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Animals/MA_Chicken<PERSON>oop", "m_displayName": "chicken coop", "m_description": "Cock a doodle dont", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionAnimalHouse|Aesthetic|Entrance", "m_nutrition": "", "m_attack": "0.15000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e32f3624702e0214687", "m_prefabName": "MA_Clothes_Bandit_A_Male_Body", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "61.00", "m_price": "20.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Bandit_A_Male_Body", "m_displayName": "MA_Clothes_Bandit_A_Male_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.10000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e32f3624702e021468e", "m_prefabName": "MA_<PERSON><PERSON>hes_Bandit_A_Male_Boots", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "45.25", "m_price": "15.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Bandit_A_Male_Boots", "m_displayName": "MA_<PERSON><PERSON>hes_Bandit_A_Male_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e32f3624702e0214695", "m_prefabName": "MA_<PERSON>lothes_Bandit_A_Male_Hands", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "61.00", "m_price": "20.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Bandit_A_Male_Hands", "m_displayName": "MA_<PERSON>lothes_Bandit_A_Male_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e32f3624702e021469c", "m_prefabName": "MA_Clothes_Bandit_A_Male_Head", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "42.50", "m_price": "14.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Bandit_A_Male_Head", "m_displayName": "MA_Clothes_Bandit_A_Male_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e33f3624702e02146a3", "m_prefabName": "MA_Clothes_Bandit_A_Male_Pants", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "58.50", "m_price": "19.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Bandit_A_Male_Pants", "m_displayName": "MA_Clothes_Bandit_A_Male_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.08000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02375b5e67e02e446f285", "m_prefabName": "MA_Clothes_Council_A_Body", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "61.00", "m_price": "20.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Council_A_Body", "m_displayName": "MA_Clothes_Council_A_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.10000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f28b", "m_prefabName": "MA_<PERSON>lothes_Council_A_Boots", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "45.25", "m_price": "15.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Council_A_Boots", "m_displayName": "MA_<PERSON>lothes_Council_A_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f291", "m_prefabName": "MA_<PERSON>lothes_Council_A_Hat", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "42.50", "m_price": "14.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Council_A_Hat", "m_displayName": "MA_<PERSON>lothes_Council_A_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f297", "m_prefabName": "MA_Clothes_Council_A_Pants", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "58.50", "m_price": "19.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Council_A_Pants", "m_displayName": "MA_Clothes_Council_A_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.08000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f29d", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Body", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "70.50", "m_price": "23.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "8.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_C<PERSON><PERSON>_Lord_B_Body", "m_displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.20000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2a3", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "54.25", "m_price": "18.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_<PERSON><PERSON><PERSON>_Lord_<PERSON>_<PERSON>", "m_displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2a9", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "51.00", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/<PERSON>_<PERSON><PERSON><PERSON>_Lord_B_Hat", "m_displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.11000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2af", "m_prefabName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "66.50", "m_price": "22.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_C<PERSON><PERSON>_Lord_<PERSON>_<PERSON>", "m_displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.12000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6823469029e27102efafd6e5", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Body", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "61.50", "m_price": "21.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_A_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.10000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68234809950f7502e49f5e7b", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Hat", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "41.00", "m_price": "14.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_A_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "683594352f149b0300426914", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Head", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "41.00", "m_price": "13.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_A_Head", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682349d734e28802e7c11c70", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Pants", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "57.00", "m_price": "19.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_A_Pants", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_A_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.08000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682346c505d4f202c2ff6f8f", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_B_Body", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "72.50", "m_price": "24.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "8.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_B_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_B_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.20000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6823488886adb202fe465455", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_B_Hat", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "43.50", "m_price": "14.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_B_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Pirate_B_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.11000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6822198840ba4e030d1ba779", "m_prefabName": "MA_<PERSON><PERSON><PERSON>_Pirate_C_Body", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "74.00", "m_price": "24.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "8.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_C_Body", "m_displayName": "MA_<PERSON><PERSON><PERSON>_Pirate_C_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.21000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68234a2e86adb202fe465d6e", "m_prefabName": "MA_<PERSON><PERSON><PERSON>_Pirate_C_Pants", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "57.50", "m_price": "19.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Pirate_C_Pants", "m_displayName": "MA_<PERSON><PERSON><PERSON>_Pirate_C_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.15000", "m_defence": "0.00000", "m_beauty": "0.12000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2b5", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Body", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "80.00", "m_price": "26.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "10.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Royal_B_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.25000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2bb", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Boots", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "65.00", "m_price": "21.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Royal_B_Boots", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.10000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2c1", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Hat", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "60.00", "m_price": "20.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Royal_B_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.12000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e33f3624702e02146aa", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Male_Hands", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "37.45", "m_price": "12.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Royal_B_Male_Hands", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Male_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2c7", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Pants", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "75.00", "m_price": "25.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "8.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Royal_B_Pants", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Royal_B_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.18000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68148d0905633a02b0f351c7", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerA_Female_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68148d0905633a02b0f351ce", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Feet", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "31.75", "m_price": "10.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerA_Female_Feet", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68148d0905633a02b0f351d5", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Head", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.25", "m_price": "11.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerA_Female_Head", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68148d0905633a02b0f351dc", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Legs", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.50", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerA_Female_Legs", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerA_Female_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e33f3624702e02146b1", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "17.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_A_Male_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e33f3624702e02146b8", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Boots", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "37.45", "m_price": "12.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_A_Male_Boots", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146c0", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Hat", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.25", "m_price": "11.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_A_Male_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146c9", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Pants", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.50", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_A_Male_Pants", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_A_Male_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68271e526ef5c71bd22a787b", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "17.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerB_Female_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6827238e4b6a3302ceda87f2", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Feet", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "31.75", "m_price": "10.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerB_Female_Feet", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682724eaad7ab002c3961224", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Head", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.00", "m_price": "11.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerB_Female_Head", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68272520d42bfd02e05c85a8", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Legs", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.00", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerB_Female_Legs", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerB_Female_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146d0", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "16.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_B_Male_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146d7", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Boots", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "37.45", "m_price": "12.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_B_Male_Boots", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146ec", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Hands", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "37.45", "m_price": "12.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_B_Male_Hands", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Hands", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146de", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Hat", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.25", "m_price": "11.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_B_Male_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ec0e34f3624702e02146e5", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Pants", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.50", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_B_Male_Pants", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_B_Male_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2cd", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "17.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_C_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.05000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2d3", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Boots", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "37.45", "m_price": "12.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_C_Boots", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Boots", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68271e5fe3f46602de1cb990", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Body", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Body", "m_sellingPrice": "52.00", "m_price": "17.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerC_Female_Body", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Body", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.50000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682723bf02ad8e0acef2b379", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Feet", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "31.75", "m_price": "10.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerC_Female_Feet", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Feet", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682724fdee75e102be34b0e3", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Head", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.25", "m_price": "11.50", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerC_Female_Head", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Head", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6827252b947426030fdbb8a6", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Legs", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.00", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_WorkerC_Female_Legs", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_WorkerC_Female_Legs", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2d9", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Hat", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:Head", "m_sellingPrice": "34.25", "m_price": "11.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_C_Hat", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Hat", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02376b5e67e02e446f2df", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Pants", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "51.50", "m_price": "17.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_C_Pants", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02377b5e67e02e446f2e5", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Shoes", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON>", "m_sellingPrice": "31.75", "m_price": "11.00", "m_workerTimeToMake": "40.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Clothes_Worker_C_Shoes", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Worker_C_Shoes", "m_description": "", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.01000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682c6a60dbb48d02bcd723a3", "m_prefabName": "MA_Costume_WorkerA_Female_Pants", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "Clothing:<PERSON><PERSON>", "m_sellingPrice": "1.00", "m_price": "1.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "False", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Costume_WorkerA_Female_Pants", "m_displayName": "MA_Costume_WorkerA_Female_Pants", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.02000", "m_warmth": "0.10000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d851d40eb257032967eb6a", "m_prefabName": "MA_CottonFarm_Action", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "0.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Action", "m_displayName": "CottonFarm_Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionCottonFarm|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b857279fb3dd0322bea1a6", "m_prefabName": "MA_CottonFarm_Chimney", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Chimney", "m_displayName": "CottonFarm_Chimney", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b857349fb3dd0322bea1ca", "m_prefabName": "MA_CottonFarm_Door", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Door", "m_displayName": "CottonFarm_Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b8573b80b2da0305f1a2ac", "m_prefabName": "MA_CottonFarm_Roof", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Roof", "m_displayName": "CottonFarm_Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b857429fb3dd0322bea1ea", "m_prefabName": "MA_CottonFarm_Shade", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Shade", "m_displayName": "CottonFarm_Shade", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b857480a2efe02f8473eaf", "m_prefabName": "MA_CottonFarm_WaterWheel", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_WaterWheel", "m_displayName": "CottonFarm_WaterWheel", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionCottonFarm|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b8574e0a2efe02f8473eb8", "m_prefabName": "MA_CottonFarm_Window", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm Cotton:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonFarm/MA_CottonFarm_Window", "m_displayName": "CottonFarm_Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d851d8e0852902d6796ec5", "m_prefabName": "MA_CottonMill_Action", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_Action", "m_displayName": "CottonMill_Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionCottonSpinner|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d8598cbd2ea203002324ee", "m_prefabName": "MA_CottonMill_Chimney", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_Chimney", "m_displayName": "CottonMill_Chimney", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d85877e782d50300869a98", "m_prefabName": "MA_CottonMill_DomeRoof", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_DomeRoof", "m_displayName": "CottonMill_DomeRoof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d856fae782d503008696e7", "m_prefabName": "MA_CottonMill_Door", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_Door", "m_displayName": "CottonMill_Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d85703cfa09102c094d520", "m_prefabName": "MA_CottonMill_Roof", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_Roof", "m_displayName": "CottonMill_Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d856ffcfa09102c094d51b", "m_prefabName": "MA_CottonMill_Window", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Cotton :Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_CottonMill/MA_CottonMill_Window", "m_displayName": "CottonMill_Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6515a1a00736150028bdb841", "m_prefabName": "MA_CryptDoor", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptDoor", "m_displayName": "MA_CryptDoor", "m_description": "Thriller...thriller nights...", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionCrypt|Entrance", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "659fd8a72e440f0029685cd7", "m_prefabName": "MA_CryptDoor_new", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptDoor_new", "m_displayName": "MA_CryptDoor_new", "m_description": "Thriller...thriller nights...", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "659ff4b28233e200288cbaf3", "m_prefabName": "MA_CryptDoor_nospawn", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptDoor_nospawn", "m_displayName": "MA_CryptDoor_nospawn", "m_description": "No monsters, just for decoration", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "6515af09bf7119002833e214", "m_prefabName": "MA_CryptGraveyard", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptGraveyard", "m_displayName": "MA_CryptGraveyard", "m_description": "Thriller...thriller nights...", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionGraveyard", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "65a11b5d3fc0f000273d9740", "m_prefabName": "MA_CryptGraveyard_nospawn", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptGraveyard_nospawn", "m_displayName": "MA_CryptGraveyard_nospawn", "m_description": "No zombies, just for decoration", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "659fd8c34dd0d5002786d378", "m_prefabName": "MA_CryptRoof", "m_buildingPartType": "NGBank", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Crypt/MA_CryptRoof", "m_displayName": "MA_CryptRoof", "m_description": "Thriller...thriller nights...", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "653fcec11bb75b00278f5493", "m_prefabName": "MA_Cutlery_DinnerFork", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "7.50", "m_price": "5.93", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_DinnerFork", "m_displayName": "Dinner Fork", "m_description": "Pin it down and cut it into tiny pieces. Whatever you're eating, this is your boy. Unless you're eating soup. Soup's for losers.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.20000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcec883bc8e0028452f80", "m_prefabName": "MA_Cutlery_DinnerKnife", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "9.50", "m_price": "6.41", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_DinnerKnife", "m_displayName": "Dinner Knife", "m_description": "Slice don't stab. If you're the sort to stab their meal, you're the sort who doesn't deserve a knife such as this.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.30000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fced083bc8e0028452f8e", "m_prefabName": "MA_Cutlery_DinnerSpoon", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "9.50", "m_price": "6.11", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_DinnerSpoon", "m_displayName": "Dinner Spoon", "m_description": "Highly recommended for all liquid format meals. Also makes a great sound if you hit someone between the eyes with one. A sort of dull yet deeply satisfying thud.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.10000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fced851f1b9002882c8ef", "m_prefabName": "MA_Cutlery_Knife_A", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "8.50", "m_price": "6.00", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_Knife_A", "m_displayName": "Chef's Knife", "m_description": "They'll never again complain about your cooking when you've this sweet slicer by your side.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.35000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcee566fa04002871fd84", "m_prefabName": "MA_Cutler<PERSON>_Knife_B", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "9.50", "m_price": "6.15", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_Knife_B", "m_displayName": "Cleaver", "m_description": "Sharp enough to cut halfway through a finger or one eighth of the way through a thigh.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.25000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fceec9e1a5300277e520f", "m_prefabName": "MA_Cutlery_Ladle", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "9.50", "m_price": "6.38", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_Ladle", "m_displayName": "Metal Ladle", "m_description": "For spooning it out into your guests' bowls. And for stoving useless kitchen hands' heads in.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.10000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcef61bb75b00278f557b", "m_prefabName": "MA_Cutlery_WoodenSpoon", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Cutlery", "m_sellingPrice": "7.60", "m_price": "6.56", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Cutlery/MA_Cutlery_WoodenSpoon", "m_displayName": "<PERSON><PERSON>", "m_description": "Stir it up, little darling, stir it up.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.16000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcb666ad1f40027ec5ff4", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON>eese", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Dairy", "m_sellingPrice": "6.90", "m_price": "3.00", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Dairy/MA_Dairy_Cheese", "m_displayName": "Wheel of Cheese", "m_description": "You like cheese? You better like cheese. You better like cheese more than anything else in the world, because this bad boy ain't gonna eat itself, you get my drift?", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14200", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcb70684cae0028dc58b4", "m_prefabName": "MA_Dairy_CheeseSlice", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Dairy", "m_sellingPrice": "7.20", "m_price": "3.25", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Dairy/MA_Dairy_CheeseSlice", "m_displayName": "Wedgy <PERSON>eese", "m_description": "So you don't like cheese enough for the big bertha Cheese Wheel, huh? Say hello to its little friend.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14800", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcb7746d6670027515be4", "m_prefabName": "MA_Dairy_Egg", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Dairy", "m_sellingPrice": "8.00", "m_price": "5.48", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.45", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Dairy/MA_Dairy_Egg", "m_displayName": "Fried Egg", "m_description": "What came first, the chicken of the egg? Who the hell cares? Egg, dammit!", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14600", "m_attack": "0.02000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcb7fd829d60027c68d60", "m_prefabName": "MA_<PERSON><PERSON>_MilkJar", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Dairy", "m_sellingPrice": "8.10", "m_price": "5.44", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Dairy/MA_Dairy_MilkJar", "m_displayName": "Milk Jar", "m_description": "Buy it, drink it, bog off.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14500", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fcb86d829d60027c68d6d", "m_prefabName": "MA_Dairy_StinkingCheese", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Dairy", "m_sellingPrice": "7.25", "m_price": "5.63", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Dairy/MA_Dairy_StinkingCheese", "m_displayName": "<PERSON>'s <PERSON><PERSON>", "m_description": "Smells worse than you. If it doesn't, rub some of this on your skin to disguise your own foul funk.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.15000", "m_attack": "0.10000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6821cde9cab37002d6c15967", "m_prefabName": "MA_DeliverToQuestPile", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_DeliverToQuestPile", "m_displayName": "<PERSON>", "m_description": "This is a quest pile", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|QuestActionGatherer|QuestStockOut|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65003d5d43ff000028dab426", "m_prefabName": "MA_Dispatch_Clock", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Aesthetic|Dispatch Cotton:Aesthetic|Dispatch Metal:Aesthetic|Dispatch Swamp :Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Clock", "m_displayName": "Clock", "m_description": "Clock watchers lament, this timepiece keeps perfect time to the second, every second! No chance of skiving off work when this beaut is ticking of the tiks!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a6aa5fa364f600276c4b12", "m_prefabName": "MA_Dispatch_Door", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Door", "m_displayName": "Food Dispatch Door", "m_description": "These simple, basic doors are made of sturdy stuff, as they ought to be when trade keeps barging through them with yet more products for the cart.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFoodDispatch|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67ea70eed34e7802ccd0d6bd", "m_prefabName": "MA_Dispatch_DoorCotton", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Cotton:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_DoorCotton", "m_displayName": "Cotton Dispatch Door", "m_description": "Sell the Clothes etc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFabricDispatch|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65003d2f8887de0028744cfc", "m_prefabName": "MA_Dispatch_Foundation", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Aesthetic|Dispatch Cotton:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Foundation", "m_displayName": "Decking", "m_description": "Sit out on the veranda and watch the world go by. Or get the hell out of the way if this is in your dispatch - no slacking on the job!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Box", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65003d41cb529a0027e5e8b1", "m_prefabName": "MA_Dispatch_Foundation_Stair_A", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Aesthetic|Dispatch Cotton:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Foundation_Stair_A", "m_displayName": "Steps", "m_description": "One short step for mankind, one of a thousand short steps for workerkind. These steps can take the brunt of even the heaviest of feet, every minute of every hour of every day.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65003d5063e1b60029aeaaa4", "m_prefabName": "MA_Dispatch_Foundation_Stair_B", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Aesthetic|Dispatch Cotton:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Foundation_Stair_B", "m_displayName": "<PERSON><PERSON>", "m_description": "Make it easier on your teams, use the ramp instead of the stairs. A simple incline for a simple staff.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b39f517855a600277efa6c", "m_prefabName": "MA_Dispatch_Metal_Door", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Action|Dispatch Metal:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Door", "m_displayName": " Dispatch Metal Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionMetalDispatch|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b39f618954ad0027793c23", "m_prefabName": "MA_Dispatch_Metal_Foundation", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Foundation", "m_displayName": "Dispatch Metal Foundation", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Box", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b39f6f967ae100291d445c", "m_prefabName": "MA_Dispatch_Metal_Foundation_Stair", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Foundation_Stair", "m_displayName": "Dispatch Metal Foundation Stair", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b38f80383364002849b76d", "m_prefabName": "MA_Dispatch_Metal_Roof", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Roof", "m_displayName": "Dispatch Metal Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b39f79ef14bf00288a4008", "m_prefabName": "MA_Dispatch_Metal_Shade", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Shade", "m_displayName": "Dispatch Metal Shade", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b39f863518b1002ab350a4", "m_prefabName": "MA_Dispatch_Metal_Window", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Metal:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Metal/MA_Dispatch_Metal_Window", "m_displayName": "Dispatch Metal Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a6aa6bed7489002b23b14e", "m_prefabName": "MA_Dispatch_RoofTileTop", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_RoofTileTop", "m_displayName": "Slate Roof & Weather Vane", "m_description": "The genteel charm of slate tiles, coupled with the quintessential Albion roof top dressing, the weathervane. What could be more appealing?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a6aa7f43e946002816d199", "m_prefabName": "MA_Dispatch_Shade", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Shade", "m_displayName": "<PERSON><PERSON> Lean-to", "m_description": "Escape the worst of the day's heat, duck under this lean-to. But don't dilly dally, there's work to be done.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67890594cb6b4103158bdbd1", "m_prefabName": "MA_Dispatch_Swamp_Door", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Action|Dispatch Swamp :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Door", "m_displayName": "Swamp Dispatch Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFoodDispatch|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678905a1cb6b4103158bdbf6", "m_prefabName": "MA_Dispatch_Swamp_Foundation", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Foundation", "m_displayName": "Swamp Dispatch Decking", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Box", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678905a9a9f38802eb931de9", "m_prefabName": "MA_Dispatch_Swamp_Foundation_Stair", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Foundation_Stair", "m_displayName": "Swamp Dispatch Steps", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678905aeefac9e030ed85f21", "m_prefabName": "MA_Dispatch_Swamp_Roof", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Roof", "m_displayName": "Swamp Dispatch Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678905b4efac9e030ed85f2d", "m_prefabName": "MA_Dispatch_Swamp_Shade", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Shade", "m_displayName": "Swamp Dispatch Shade", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "678905b9cb6b4103158bdc44", "m_prefabName": "MA_Dispatch_Swamp_Window", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch_Swamp/MA_Dispatch_Swamp_Window", "m_displayName": "Swamp Dispatch Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a6aa8aec2ef40027b2e780", "m_prefabName": "MA_Dispatch_Window", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Window", "m_displayName": "Dispatch Storeroom", "m_description": "A Simple Windowless Storeroom", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a6aa75ec2ef40027b2e710", "m_prefabName": "MA_Dispatch_Window_Small", "m_buildingPartType": "NGDispatch", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Dispatch/MA_Dispatch_Window_Small", "m_displayName": "Dispatch Storage", "m_description": "A Simple Storeroom", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d034ec52f38802cd05cd68", "m_prefabName": "MA_Factory_Action", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_Factory_Action", "m_displayName": "Factory Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFactory|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "672b8f52411a2402c057a88d", "m_prefabName": "MA_FactoryActionCogs", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryActionCogs", "m_displayName": "Factory Cogs", "m_description": "boost", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "WorkerDouble", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6627d50ba12a510027af38a0", "m_prefabName": "MA_FactoryActionPiston", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryActionPiston", "m_displayName": "Factory Pump", "m_description": "Keep your factory fully inflated with this self explanatory wheel pump.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFactory", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650185ab7d831b0028075659", "m_prefabName": "MA_FactoryArch_4x8_long", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryArch_4x8_long", "m_displayName": "MA_FactoryArch_4x8_long", "m_description": "MA_FactoryArch_4x8_long", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145640dcba0d00285d3985", "m_prefabName": "MA_FactoryArches", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryArches", "m_displayName": "MA_FactoryArches", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "68232e60c246bc031be30f52", "m_prefabName": "MA_Factory_Boiler", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_Factory_Boiler", "m_displayName": "Factory Boiler", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFactory", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650185c3b35db80027983e09", "m_prefabName": "MA_FactoryChimA", "m_buildingPartType": "NGFactory", "m_rarity": "Epic", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryChimA", "m_displayName": "Tall Brick <PERSON>", "m_description": "Brick skies at night, steeplejack's delight! Mind how you go at the top, you'll burn your face clean off if you're not careful.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65a8f9ddae78dc0029aeacfe", "m_prefabName": "MA_FactoryChimB", "m_buildingPartType": "NGFactory", "m_rarity": "Epic", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryChimB", "m_displayName": "Grimy Factory Chimney", "m_description": "Blackened by soot, the layer of filth on this stack acts like glue, binding the bricks ever tighter together.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145e213b7d500027df1e82", "m_prefabName": "MA_FactoryDoor_4x4_A", "m_buildingPartType": "NGFactory", "m_rarity": "Epic", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryDoor_4x4_A", "m_displayName": "MA_FactoryDoor_4x4_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651456b2cd136b0027c8212a", "m_prefabName": "MA_FactoryDoorArchBase", "m_buildingPartType": "NGFactory", "m_rarity": "Epic", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryDoorArchBase", "m_displayName": "Red Brick Arched Factory Door", "m_description": "An imposing cast iron frontage for your imposing factory. Enter here and be ready to work your socks off.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651456fe1b06cc0027ca04fc", "m_prefabName": "MA_FactoryDoorArchTop", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryDoorArchTop", "m_displayName": "MA_FactoryDoorArchTop", "m_description": "A lovely top for an arch", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650185d416308500271791c0", "m_prefabName": "MA_FactoryDoor_Tall", "m_buildingPartType": "NGFactory", "m_rarity": "Epic", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryDoor_Tall", "m_displayName": "Imposing Brick Door", "m_description": "This gargantuan entranceway speaks volumes about the building it nestles within. Commerce, industry, progress it bellows as you cross its threshold. ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6508271a473130002603b62d", "m_prefabName": "<PERSON>_<PERSON><PERSON><PERSON>_Tall_Pretty", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryDoor_Tall_Pretty", "m_displayName": "<PERSON>_<PERSON><PERSON><PERSON>_Tall_Pretty", "m_description": "Factory door with no components. For use as dupes", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Happiness", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651457d81e71200027f90430", "m_prefabName": "MA_FactoryGeneric", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryGeneric", "m_displayName": "MA_FactoryGeneric", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145b82dcba0d00285de191", "m_prefabName": "MA_FactoryGeneric_Half", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryGeneric_Half", "m_displayName": "MA_FactoryGeneric_Half", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145de41b06cc0027ca98eb", "m_prefabName": "MA_FactoryRoofSemi_A", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSemi_A", "m_displayName": "Fenced Brick Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "672cb2de43e5ed031eb05784", "m_prefabName": "MA_FactoryRoofSemi_AWkr", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSemi_AWkr", "m_displayName": "Fenced Brick Roof With Work Station", "m_description": "Fenced Brick Roof With Work Station", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650185e4e5c131002abbe532", "m_prefabName": "MA_FactoryRoofSlant_A", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSlant_A", "m_displayName": "Low Sloping Factory Slate Roof", "m_description": "Easy on the eye, hewn from local rocks, this slate roof is highly effective at keeping the worst of the weather out and the hottest of the heat in.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650185f87d831b002807580d", "m_prefabName": "MA_FactoryRoofSlant_B", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSlant_B", "m_displayName": "Slate Tiled Roof & Iron Railings", "m_description": "Dominate the landscape, shape the world the way you want it, impose your authority on your surroundings. House your factories beneath this imperious roofing block.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145d6819112d002a9adbfb", "m_prefabName": "MA_FactoryRoofSlant_ChimA", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSlant_ChimA", "m_displayName": "MA_FactoryRoofSlant_ChimA", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145e9c6e940a0027a38a07", "m_prefabName": "MA_FactoryRoofSlantHalf_A", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryRoofSlantHalf_A", "m_displayName": "MA_FactoryRoofSlantHalf_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145d9f0a258a00281053f4", "m_prefabName": "MA_FactoryVents", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryVents", "m_displayName": "MA_FactoryVents", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6501861bb5410a00275e9f77", "m_prefabName": "MA_FactoryWindowArches", "m_buildingPartType": "NGFactory", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindowArches", "m_displayName": "MA_FactoryWindowArches", "m_description": "MA_FactoryWindowArches", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145fcc040a18002876a801", "m_prefabName": "MA_FactoryWindowArches_Double", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindowArches_Double", "m_displayName": "MA_FactoryWindowArches_Double", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145ffa6e940a0027a38ba6", "m_prefabName": "MA_FactoryWindowArches_Quad", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindowArches_Quad", "m_displayName": "2 X Workspace Block", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "WorkerDouble", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65083318614dd80028851a9c", "m_prefabName": "MA_FactoryWindowArches_Worker", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindowArches_Worker", "m_displayName": "Lead lined Factory Windows", "m_description": "A building block of immense value, each one allows for an additional worker to take up the slack and grind their bones to dust in service of your march towards untold riches!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651460193c7c9a0028ebe58b", "m_prefabName": "MA_FactoryWindows_4_A", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindows_4_A", "m_displayName": "MA_FactoryWindows_4_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65145e5226f3190026446fce", "m_prefabName": "MA_FactoryWindows_4_B", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindows_4_B", "m_displayName": "MA_FactoryWindows_4_B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6514603719112d002a9ae341", "m_prefabName": "MA_FactoryWindows_8", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindows_8", "m_displayName": "MA_FactoryWindows_8", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6514605e3c7c9a0028ebe62a", "m_prefabName": "MA_FactoryWindows_Half", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindows_Half", "m_displayName": "MA_FactoryWindows_Half", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651460857b<PERSON>260028<PERSON><PERSON><PERSON>", "m_prefabName": "MA_FactoryWindowVents", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Factory/MA_FactoryWindowVents", "m_displayName": "MA_FactoryWindowVents", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "1.50", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67f7a5117577bf02d5e5d328", "m_prefabName": "MA_Farm_Action", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Action", "m_displayName": "Farm Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionWheatFarm|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "666ad750ad60810029780df7", "m_prefabName": "MA_Farm_Action_WeatherVane", "m_buildingPartType": "NGProduceMine", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Action_WeatherVane", "m_displayName": "<PERSON>", "m_description": "Cock-a-doodle-do!...Or don't, it's up to you.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.25000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f7074b43240000266402ff", "m_prefabName": "MA_Farm_Door", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Door", "m_displayName": "Farmhouse Thatched Door", "m_description": "Wipe your feet on the way in. Don't you be spreading none of that muck around inside the farmhouse – leave that filth out in the yard where it belongs.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6570803735be1000268b4e67", "m_prefabName": "MA_Farm_Door_WM", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Door_WM", "m_displayName": "MA_Wood_Mine_Door", "m_description": "MA_Wood_Mill_Door", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f707e05cf1d60029fdbe4c", "m_prefabName": "MA_Farm_Hotspot", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Hotspot", "m_displayName": "Wheat Field", "m_description": "A simple, youthful swain am I,\nWho love at fancy's pleasure;\nI fondly watch the blooming wheat,\nAnd others reap the treasure…", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "655211b21f504200271df1ab", "m_prefabName": "MA_Farm_Hotspot_Vegs", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Hotspot_Vegs", "m_displayName": "Vegetable Plot ", "m_description": "Juicy tomatoes. They're fruit! But if you try and put one in a yoghurt, you'll get a biff on your boffin.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65d76162648c7e002891a42e", "m_prefabName": "MA_Farm_Hotspot_Vegs2", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Hotspot_Vegs2", "m_displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON> and Carrot Plot", "m_description": "Oats and beans and barley grow,\nYet here there's things to shower,\nThere's things of which you ought to know,\nI've sewn a seed of cauliflower.\n", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f707fafe016a0028aa6d8a", "m_prefabName": "MA_Farm_RoofTop", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_RoofTop", "m_displayName": "Thatched Farm Roof", "m_description": "A beautiful top for a rustic farmhouse.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f708141438cf0026d66278", "m_prefabName": "MA_Farm_RoofWindowBig", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_RoofWindowBig", "m_displayName": "Medieval Farm Window", "m_description": "Wattle and daub and old fashioned charm. Goes up like pig fat if you torch it.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6720b4019a6e4002d075b6ac", "m_prefabName": "MA_Farm_RoofWindowBig_NW", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_RoofWindowBig_NW", "m_displayName": "A simple storeroom", "m_description": "Put things in here", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f7082e038ae50028cf0a67", "m_prefabName": "MA_Farm_Shade", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Shade", "m_displayName": "Farm Leaner", "m_description": "those in the trade know only too well, you can never have enough storage spaces on a farm.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f70846170c380027e285c7", "m_prefabName": "MA_Farm_SideRoof", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_SideRoof", "m_displayName": "Grey Stone Farm Chimney", "m_description": "Spying one of these traditional farmhouse chimneys as you breast a hill, smoke wafting gently out, fair makes the heart flutter.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f708f4170c380027e2892e", "m_prefabName": "MA_Farm_SideWindow", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_SideWindow", "m_displayName": "MA_Farm_SideWindow", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f7090a170c380027e289d6", "m_prefabName": "MA_Farm_Storage", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Storage", "m_displayName": "MA_Farm_Storage", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Box", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "650d9867057acc0027da3b96", "m_prefabName": "MA_Farm_Storage_Worker", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Storage_Worker", "m_displayName": "Farm Shed", "m_description": "You might think this is where they store farm tools, but you'd be wrong. This here is a classic farmer's hidey hole. Every self respecting land worker needs a little place to get away from it all.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f709194ea73e0027fedf4f", "m_prefabName": "MA_Farm_Window", "m_buildingPartType": "NGProduceMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Farm/MA_Farm_Window", "m_displayName": "MA_Farm_Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6629290a64b13500291e07b8", "m_prefabName": "MA_Glob1", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "6.80", "m_price": "4.13", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Glob1", "m_displayName": "Globublobu", "m_description": "You can't make a broth without a blob of glob. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.11000", "m_attack": "0.30000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66292935bc06d10029386503", "m_prefabName": "MA_Glob2", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "7.00", "m_price": "4.31", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Glob2", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "In the early part of the 14th century, King <PERSON><PERSON><PERSON> ruled with an iron fist. This period of time was known as Glob Rule, and this delightful jus was made in honour of that titan of Albion history. The Globule.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.11500", "m_attack": "0.20000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "670e89cb25273502c98c3b42", "m_prefabName": "MA_HeroesGuild_Bedroom", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Heroes Guild:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_HeroesGuild/MA_HeroesGuild_Bedroom", "m_displayName": "HeroesGuild Bedroom", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "GuildBedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "670e4140bcc28602f61f9486", "m_prefabName": "MA_HeroesGuild_Door", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Heroes Guild:Doors", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_HeroesGuild/MA_HeroesGuild_Door", "m_displayName": "HeroesGuild Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHeroesGuild|Entrance", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "671123529e042302b3ed1609", "m_prefabName": "MA_HeroesGuild_Healing", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Heroes Guild:Support", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_HeroesGuild/MA_HeroesGuild_Healing", "m_displayName": "HeroesGuild Healing", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "GuildHealing", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67251a42327af702d707217e", "m_prefabName": "MA_HeroesGuild_Roof", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Heroes Guild:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_HeroesGuild/MA_HeroesGuild_Roof", "m_displayName": "Heroes' Guild Roof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6711236ee9d4d902ec795a7d", "m_prefabName": "MA_HeroesGuild_Training", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Heroes Guild:Support", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_HeroesGuild/MA_HeroesGuild_Training", "m_displayName": "HeroesGuild Training", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "GuildTraining", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66ab50b1b70cd00027254aed", "m_prefabName": "MA_LumberMill_Door", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_Door", "m_displayName": "Lumber Mill Door", "m_description": "Tress go in, lumber comes out.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66ab51c26d694a0028c4ba15", "m_prefabName": "MA_LumberMill_RoofSide", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_RoofSide", "m_displayName": "Lumber Mill Roof Side", "m_description": "Slated skillion roof for the Lumber Mill. Posh word, that. Me nan calls 'em lean tos.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66ab54c8f29d640029a573f3", "m_prefabName": "MA_LumberMill_RoofTop", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_RoofTop", "m_displayName": "Lumber Mill Roof Top", "m_description": "A stylish, beach tiled gabled roof for the Lumber Mill. Cost you an arm and a leg in the city that wood.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b48bfcf687e50027ebf92d", "m_prefabName": "MA_LumberMill_Storage", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_Storage", "m_displayName": "Lumber Mill Storage", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66ab55ad1d95e6002732b963", "m_prefabName": "MA_LumberMill_Window", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_Window", "m_displayName": "Lumber Mill Window", "m_description": "Log jam your workers into the mill, get them down and dirty in the saw dust. ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66ab5687a9dff20027399def", "m_prefabName": "MA_<PERSON>mber<PERSON>ill_Wood_Processor", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Lumber Mill:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_LumberMill/MA_LumberMill_Wood_Processor", "m_displayName": "Lumber Mill Processor", "m_description": "Sure, it looks to you like it's a big empty space, but you are a buffoon with no knowledge of the ways of wood. Go on, clear off.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionLumberMill|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "663354cbd18ba1002810acc2", "m_prefabName": "<PERSON>_<PERSON><PERSON>_Chicken", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "6.80", "m_price": "5.35", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Lumps_Chicken", "m_displayName": "Chicken Lumps", "m_description": "Lumpy chicken treats. Each lump was cut from the back of the fowl and rolled in a protective layer of refinery dirt, making sure you get the best ingredients every single time.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.08000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633757fd48636002847c08c", "m_prefabName": "<PERSON>_<PERSON><PERSON>_Fish", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "6.00", "m_price": "4.35", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Lumps_Fish", "m_displayName": "Fish Chunks", "m_description": "But fish don't blow?", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.11600", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "663354f25a356500279bdfb4", "m_prefabName": "MA_Lumps_Meat", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "7.10", "m_price": "4.43", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Lumps_Meat", "m_displayName": "Meat Lumps", "m_description": "Lumpty Dumpty had a great fall. Now eat yer meat and don't ask anymore questions, lest you fancy falling into the same bag of knives this fella did.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.11800", "m_attack": "0.10000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66337591d18ba10028127d06", "m_prefabName": "MA_Lumps_Rat", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "3.75", "m_price": "0.01", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Lumps_Rat", "m_displayName": "Rat Meat", "m_description": "Rat-a-tat-tat, got some meat in my hat. Where did I get it? I found it out the back, it was eating out the bins until it got whacked.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.02200", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "663375b0bc105400290ab7d3", "m_prefabName": "MA_Lumps_Veg", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "7.30", "m_price": "4.61", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Lumps_Veg", "m_displayName": "Vegetable Medley", "m_description": "Vegetables. Poor people eat them. They're like meat if you buried it and let stuff grow out of it and then threw it in the bin and ate whatever's left.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.12300", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d02377b5e67e02e446f2eb", "m_prefabName": "<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Mannequin", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Clothing", "m_mADrawerInfos": "", "m_sellingPrice": "5.00", "m_price": "5.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "45.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON><PERSON>", "m_starterPack": "True", "m_drawer": 1, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "4.00", "m_prefabPath": "_CurrentlyUsed/_MA_Clothes/MA_Male_C<PERSON><PERSON>_Mannequin", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67f91a0266665457878689f2", "m_prefabName": "MA_MetalMine_Action", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_Action", "m_displayName": "MetalMine Action", "m_description": "Make Mine A Metal", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionMineMetal|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b26d276649f40026f4f429", "m_prefabName": "MA_MetalMine_Door_A", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Entrances|Metal Smelter:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_Door_A", "m_displayName": "Metal Worker Entrance", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66166639f749870029fca76c", "m_prefabName": "MA_MetalMine_MetalMine_A", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_MetalMine_A", "m_displayName": "Drive Shaft", "m_description": "A powerful wheel that heaves the ore and rock from the bed of the mine.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionMineMetal|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6616664dbcf0370028a15a66", "m_prefabName": "MA_MetalMine_MetalMine_B", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_MetalMine_B", "m_displayName": "<PERSON> Biter", "m_description": "Chews through ore riddled stone like a pin through paper.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66166663b946e20028ad0fbe", "m_prefabName": "MA_MetalMine_MetalSmelter_A", "m_buildingPartType": "NGMetalSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Smelter:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_MetalSmelter_A", "m_displayName": "Furnace", "m_description": "Keep back if you value your eyebrows.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionMetalSmelter|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66166668e8da6e00275a3121", "m_prefabName": "MA_MetalMine_MetalSmelter_B", "m_buildingPartType": "NGMetalSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Smelter:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_MetalSmelter_B", "m_displayName": "Smelter Fume Disperser", "m_description": "Channels heat and foul air out of the smelter.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6616666d271e29002815b675", "m_prefabName": "MA_MetalMine_MetalSmelter_C", "m_buildingPartType": "NGMetalSmelter", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Smelter:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_MetalSmelter_C", "m_displayName": "Bellows", "m_description": "Aerates the furnace, keeps the fires burning.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b26f8ae5abcf002622829a", "m_prefabName": "MA_MetalMine_Roof_A", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Roofs|Metal Smelter:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_Roof_A", "m_displayName": "MetalMine Roof A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b26f9c6649f40026f5750c", "m_prefabName": "MA_MetalMine_Roof_B", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Roofs|Metal Smelter:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_Roof_B", "m_displayName": "MetalMine Roof B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b26fa8f4b97100285563f6", "m_prefabName": "MA_MetalMine_Window_A", "m_buildingPartType": "NGMetalMine", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Mine:Workers|Metal Smelter:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalMine_Window_A", "m_displayName": "MetalMine Window A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "680a68a80e62eb02edecb742", "m_prefabName": "MA_MetalSmelter_Action", "m_buildingPartType": "NGMetalSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Metal Smelter:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_MetalMine/MA_MetalSmelter_Action", "m_displayName": "MetalSmelter Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionMetalSmelter|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64b66b986e54bb0027917b8f", "m_prefabName": "MA_OrderBoard", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Cotton:Stock|Dispatch Metal:Stock|Dispatch:Stock|Dispatch Swamp :Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 2, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Civic/MA_OrderBoard", "m_displayName": "Order Board", "m_description": "The order board. The place where all things begin. The acorn that grows into the mighty oak. ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6629296622f6560028031624", "m_prefabName": "MA_PieBaseA", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "7.20", "m_price": "4.50", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_PieBaseA", "m_displayName": "Tem-pie-rary", "m_description": "It says this is a temporary pie base. If you're playing this game and you see this description, it is no longer a temporary pie base.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.12000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc9abd829d60027c66630", "m_prefabName": "MA_Pies_PieBase", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "4.10", "m_price": "2.75", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Pies/MA_Pies_PieBase", "m_displayName": "Pie Casing", "m_description": "Hot pies, cold pies, tarts and flans, whatever your predilection, this case is the perfect base. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "61127adbc104d90bb23fa909", "m_prefabName": "MA_Pies_PieLid", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "8.90", "m_price": "5.25", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Pies/MA_Pies_PieLid", "m_displayName": "A Pie Lid", "m_description": "It ain't a pie if it ain't got a lid. Fact. Stone cold fact. Get it while it's hot.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc9c76ad1f40027ec30bd", "m_prefabName": "MA_Pies_TartFilling", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "8.00", "m_price": "5.48", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Pies/MA_Pies_TartFilling", "m_displayName": "Blueberry Tart", "m_description": "A tart case filled with rich blueberry compote. Eat it or slap it into the face of a chum for hoots and ha-has. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.14600", "m_attack": "0.10000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "653fc9ce684cae0028dc108b", "m_prefabName": "MA_Pies_TartLid", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "9.00", "m_price": "5.63", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Pies/MA_Pies_TartLid", "m_displayName": "Tart Topper", "m_description": "You don't want your tart filling to slip out in transit, do you? Get a tart lid on top and keep the gooey stuff from spilling.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.15000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67f7a6318f292802e95a14cf", "m_prefabName": "MA_Produce_Mill_Action", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Action", "m_displayName": "Mill Action", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionFlourMill|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f754e9ee084000274354fd", "m_prefabName": "MA_Produce_Mill_Block", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Block", "m_displayName": "Mill Worker Block", "m_description": "You'll get no workers in your mill without this fine daubed room.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75511b66ebb0027d50cf3", "m_prefabName": "MA_Produce_Mill_Deck", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Stock|Mill Produce:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Deck", "m_displayName": "Mill Veranda", "m_description": "Keep your mill above ground - better to keep the vermin at bay and the filth from your grain.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75525dbeb260028429f45", "m_prefabName": "MA_Produce_Mill_Dispatch", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Dispatch", "m_displayName": "Refinery Mill Door", "m_description": "In comes the raw produce, out goes the refined ingredients.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75534e17338002ab7f4f9", "m_prefabName": "MA_Produce_Mill_Door", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Door", "m_displayName": "Mill Gangway", "m_description": "A wide entranceway to allow for the comings and goings of heavy, 10kg bags of grain and farm supplies.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75544d73d920026b9bda5", "m_prefabName": "MA_Produce_Mill_Mid", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Mid", "m_displayName": "Oast House", "m_description": "The domed roof allows for the temperature to be regulated in the hopes that the grain will keep. Doesn't stop the mice and rats, mind.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75557de96130028cb55d6", "m_prefabName": "MA_Produce_Mill_Thatch", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Thatch", "m_displayName": "Low Thatched Roof", "m_description": "How low can you go? Ask a thatcher. Low enough to snatch your milk…", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f75566c4293200284178ac", "m_prefabName": "MA_Produce_Mill_Top", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Mill Produce:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Mill_Top", "m_displayName": "Windshaft & Sails", "m_description": "Old fashioned and outdated power. Wind power? Grow up.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionAnimation|Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "651e8510df3b080027e2336b", "m_prefabName": "MA_Produce_Smelter_Rustic_WaterWheel_New", "m_buildingPartType": "NGProduceSmelter", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Farm:Action", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Harvesters/MA_Produce_Smelter_Rustic_WaterWheel_New", "m_displayName": "Water Wheel", "m_description": "When there's no wind, the waterwheel propels the sails and drives the shaft. But what use is this when there's no water to power it? Progress demands new forms of power!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionWheatFarm", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "673c9bd0d41b4802b2d47b84", "m_prefabName": "MA_QuestPile", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_QuestPile", "m_displayName": "<PERSON>", "m_description": "This is a quest pile", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|QuestStockOut|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "67dc3ab0b53d2d0337c18f33", "m_prefabName": "MA_QuestStockpile", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_QuestStockpile", "m_displayName": "Quest Stockpile", "m_description": "This is a quest stockpile", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "StockIn", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "68485eaff94901221b4a31a2", "m_prefabName": "MA_QuestSwordRack", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_QuestSwordRack", "m_displayName": "Quest Weapon Rack", "m_description": "This is a quest weapon rack", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "BCActionQuestOrder|QuestStockIn", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "67334c3450b98b030ef124c0", "m_prefabName": "MA_QuestTable", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_QuestTable", "m_displayName": "Quest Table", "m_description": "This is a quest Table", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "BCActionQuestOrder|QuestStockIn", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "66b22b6a832238002873034d", "m_prefabName": "MA_Sausage_A_v2", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "4.00", "m_price": "2.25", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Sausage_A_v2", "m_displayName": "<PERSON>y'n'Beaty", "m_description": "Not all sausages are born equal. ", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06000", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092ebfde8fe0321b42418", "m_prefabName": "MA_Shop_Bazaar", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Bazaar", "m_displayName": "MA_Shop_Bazaar", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionShop", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092c2fde8fe0321b423b6", "m_prefabName": "MA_Shop_Door", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Door", "m_displayName": "MA_Shop_Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661d249efdd04100278b147b", "m_prefabName": "MA_Shop_Door_A", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Door_A", "m_displayName": "Rustic Shop Door", "m_description": "The shop door", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|ActionShop|Entrance", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092cc519a2302e0609c33", "m_prefabName": "MA_Shop_Roof_Chimney", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Roof_Chimney", "m_displayName": "MA_Shop_Roof_Chimney", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092d4519a2302e0609c3e", "m_prefabName": "MA_Shop_RoofSide", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_RoofSide", "m_displayName": "MA_Shop_RoofSide", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092ddb2928b02c02726f6", "m_prefabName": "MA_Shop_RoofWindow", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_RoofWindow", "m_displayName": "MA_Shop_RoofWindow", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092f4797f280317bacd3e", "m_prefabName": "MA_Shop_Sign_Food", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Sign_Food", "m_displayName": "MA_Shop_Sign_Food", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66d092fd2d06f002ce2a8eb7", "m_prefabName": "MA_Shop_Window", "m_buildingPartType": "NGShop", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Shop:Workers", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Shop/MA_Shop_Window", "m_displayName": "MA_Shop_Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Worker", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d58792790d002a2355f6", "m_prefabName": "MA_SlicedBeef", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "2.60", "m_price": "1.84", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedBeef", "m_displayName": "<PERSON><PERSON>", "m_description": "Sliced from fresh cow, this meaty treat is unique in that it can be eaten or worn over the face to protect the wearer from the ravages of magic. Like tinfoil, if tinfoil existed yet.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.04900", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d5da2ff9cc0027057c91", "m_prefabName": "MA_SlicedBread_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "2.30", "m_price": "1.39", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedBread_A", "m_displayName": "<PERSON><PERSON> Sliced Bread", "m_description": "Sliced bread, bread that's been cut before it reaches the buyer, is for the rich. If a poor type tries to eat a slice of this, they will instantly vomit, and their barf will spell out the word \"scum\" because poor people are not allowed nice things.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.03700", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d5ea11b5020028191a69", "m_prefabName": "MA_SlicedBread_A_Mold", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Bread", "m_sellingPrice": "1.70", "m_price": "0.01", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedBread_A_Mold", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "Selling bread that has been on the factory floor for over two weeks is both unethical and dangerous. Prolonged exposure increases the risk of contamination, leading to potential food poisoning. Prioritising safety over profit is essential for maintaining both consumer health and business integrity.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.02000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d6082d40130028680705", "m_prefabName": "MA_SlicedCheese", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "2.23", "m_price": "0.44", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedCheese", "m_displayName": "Pressed Cheese", "m_description": "Freshly run over by a passing cart. Get it while it's hot!", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.03500", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d61c45075200284447f2", "m_prefabName": "MA_SlicedChicken", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "2.50", "m_price": "1.69", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedChicken", "m_displayName": "Chicken Slice", "m_description": "Bwark! Bok, bok, bok, b-kark!\n\nWhat the fuck are chickens on about? Shut up and get in the sandwich!", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.04500", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d636798e000028e1d989", "m_prefabName": "MA_SlicedHam", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "3.70", "m_price": "1.84", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedHam", "m_displayName": "<PERSON><PERSON>", "m_description": "Take a malnourished pig, drop it from the top of a turret, voila! Slammed ham!", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.04900", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d64c32065e0027635e74", "m_prefabName": "MA_SlicedLettuce", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "1.80", "m_price": "0.35", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedLettuce", "m_displayName": "Lettuce Leaf", "m_description": "<PERSON>. How tedious. <PERSON>. Yuck.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.02300", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d66353e71000270d4bfc", "m_prefabName": "MA_SlicedOnion", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "2.70", "m_price": "1.80", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedOnion", "m_displayName": "<PERSON><PERSON>gy Onion Funion", "m_description": "So much fun packed into something so small! eat them, sure, but there's way more for to be had stuffing them into the eyes of your neighbour. Go on, live a little.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.04800", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d6745573ea002820fbb9", "m_prefabName": "MA_SlicedTom", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "2.05", "m_price": "0.38", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedTom", "m_displayName": "Tomato Slice", "m_description": "The most expensive slice of wet, red rubbish you'll ever buy.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.03000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6662d6859aff5e00298bb0b8", "m_prefabName": "MA_SlicedWerewolf", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Fillings", "m_sellingPrice": "6.80", "m_price": "1.88", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_SlicedWerewolf", "m_displayName": "Slither of Lycanthrope", "m_description": "Warning: eating this product may cause drowsiness, loss of memory and a strong desire to rip the throats out of everyone you know before feating on their gizzards.", "m_usageCap": 2, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.05000", "m_attack": "0.15000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633af1b702e8b0028f55dea", "m_prefabName": "MA_<PERSON>_Beef", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "6.30", "m_price": "4.65", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Stock_Beef", "m_displayName": "Beef Stock", "m_description": "You know they render down the bones of the cow to make this? Stinks like hell. You really gonna eat that?", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.12400", "m_attack": "0.15000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633af4430a4e70028a6350d", "m_prefabName": "MA_Stock_Chicken", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "2.75", "m_price": "0.63", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Stock_Chicken", "m_displayName": "Chicken Stock", "m_description": "Arguably the king of stocks", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.05000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633af5a30a4e70028a6356f", "m_prefabName": "MA_Stock_Fish", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "7.40", "m_price": "4.69", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Stock_Fish", "m_displayName": "Fish Stock", "m_description": "The romans used to ferment this stuff and let it fester for months before slapping it on everything.\n\nIt literally smells worse than dog puke on a used nappy.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.12500", "m_attack": "0.05000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633af6ec8112c0028aa4c7d", "m_prefabName": "MA_Stock_Rat", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "4.25", "m_price": "0.01", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Stock_Rat", "m_displayName": "<PERSON>", "m_description": "Rat stock. Utterly vile. Still smells better than that fish stuff. Strewth!", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.03000", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6633af85a0d450002516d672", "m_prefabName": "MA_Stock_Veg", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Food", "m_mADrawerInfos": "Food:Pies & Soups", "m_sellingPrice": "4.20", "m_price": "1.25", "m_workerTimeToMake": "5.0", "m_numTapsToMake": "8.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Flour", "m_starterPack": "True", "m_drawer": 0, "m_drawOrderPriority": 5, "m_drawerScale": "0.35", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Food/MA_Stock_Veg", "m_displayName": "Vegetable Stock", "m_description": "Seriously, the juice of vegetables. Why bother? Just eat the veg and forget about this wet skid mark of a dish.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.06300", "m_attack": "0.01000", "m_defence": "0.01000", "m_beauty": "0.01000", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6790dc43fed15803145f06cd", "m_prefabName": "MA_TardisCrypt", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/MA_TardisCrypt", "m_displayName": "Helm Crypt", "m_description": "This is the Crypt you are in", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance|Tap|TardisCrypt", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "67d464950667a4030aacff4e", "m_prefabName": "MA_Tavern_BriarLake_Arch", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Arch", "m_displayName": "Tavern_BriarLake_Arch", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464b52abe3402f2c09c9e", "m_prefabName": "MA_Tavern_BriarLake_Door", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 1, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Door", "m_displayName": "Tavern_BriarLake_Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464b30667a4030aacff7a", "m_prefabName": "MA_Tavern_BriarLake_Door_Stairwell", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Door_Stairwell", "m_displayName": "Tavern_BriarLake_Door_Stairwell", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464b02abe3402f2c09c90", "m_prefabName": "MA_Tavern_<PERSON><PERSON>r<PERSON><PERSON>_Roof_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Roof_A", "m_displayName": "Tavern_<PERSON><PERSON>r<PERSON><PERSON>_Roof_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464ae2abe3402f2c09c8b", "m_prefabName": "MA_Tavern_<PERSON><PERSON><PERSON><PERSON><PERSON>_Roof_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Cotton:Roofs|Tavern Cotton :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Roof_B", "m_displayName": "Tavern_<PERSON><PERSON>r<PERSON><PERSON>_Roof_B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464ac0667a4030aacff75", "m_prefabName": "MA_Tavern_BriarLake_Sign", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Actions", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Sign", "m_displayName": "Tavern_BriarLake_Sign", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionTavern|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464a90667a4030aacff62", "m_prefabName": "MA_Tavern_BriarLake_Window_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Window_A", "m_displayName": "Tavern_BriarLake_Window_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d4649b0667a4030aacff58", "m_prefabName": "MA_Tavern_BriarLake_Window_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Cotton:Aesthetic|Tavern Cotton :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Window_B", "m_displayName": "Tavern_BriarLake_Window_B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d464990667a4030aacff53", "m_prefabName": "MA_Tavern_BriarLake_Window_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Cotton :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 4, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_BriarLake/MA_Tavern_BriarLake_Window_C", "m_displayName": "Tavern_BriarLake_Window_C", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f7321ae83733002668af66", "m_prefabName": "MA_Tavern_Cellar", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_Cellar", "m_displayName": "Tavern Cellar", "m_description": "The beer's own entrance into the tavern. Barrels of ale are rolled through this ground level hatch and stashed inside ready for quafftide.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f732314ea73e0027001bb1", "m_prefabName": "MA_Tavern_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_Door_A", "m_displayName": "Standard Tavern Door", "m_description": "Your door's not on, you're not coming in.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f73240538ef1002757e167", "m_prefabName": "MA_Tavern_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_Door_B", "m_displayName": "Standard Tavern Door +", "m_description": "As common as your average standard tavern door, with a slightly different facade. Put it this way, if you were a zebra, you'd be saying \"All these doors look the same.\".", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b280d1b71b610027ca2d3c", "m_prefabName": "MA_Tavern_Metal_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Metal :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Door_A", "m_displayName": "Tavern_Metal_Door_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b280c89a863600286cfe28", "m_prefabName": "MA_Tavern_Metal_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Metal :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Door_B", "m_displayName": "Tavern_Metal_Door_B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b280bc063282002846d5ab", "m_prefabName": "MA_Tavern_Metal_Roof_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Metal :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Roof_A", "m_displayName": "Tavern_Metal_Roof_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b280b0426c9f0027b09f24", "m_prefabName": "MA_Tavern_Metal_Roof_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Roof_B", "m_displayName": "Tavern_Metal_Roof_B", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b280a5f9b39e002974524e", "m_prefabName": "MA_Tavern_Metal_Roof_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Roof_C", "m_displayName": "Tavern_Metal_Roof_C", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b2809ad2384f00276683c5", "m_prefabName": "MA_Tavern_Metal_Shade", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Shade", "m_displayName": "Tavern_Metal_Shade", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Happiness", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b28090d530ee0029ae6d3b", "m_prefabName": "MA_Tavern_Metal_Sign", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Metal :Actions", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Sign", "m_displayName": "Tavern_Metal_Sign", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionTavern|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65b28087063282002846d4b0", "m_prefabName": "MA_Tavern_Metal_Window_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Metal :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Metal/MA_Tavern_Metal_Window_A", "m_displayName": "Tavern_Metal_Window_A", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f73252fe016a0028b32430", "m_prefabName": "MA_Tavern_RoofTileTop_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_RoofTileTop_A", "m_displayName": "Rustic Tavern Roof & Loft Window", "m_description": "What finer pleasure is there than the one where you drink half your body weight in warm beer in an old fashioned, rustic, Albion tavern, then slip upstairs to the loft to open the window and shout obscenities at passing sober types?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f73262063957002a4c326d", "m_prefabName": "MA_Tavern_RoofTileTop_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_RoofTileTop_B", "m_displayName": "Rustic Tavern Roof", "m_description": "Adding roofs to your town buildings improves the look of the locale, which is important to the locals. A nice place to live is a nice place to die.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f73273538ef1002757e29c", "m_prefabName": "MA_Tavern_RoofTileTop_Chimney", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_RoofTileTop_Chimney", "m_displayName": "Rustic Tavern Roof & Chimney Breast", "m_description": "Well, you don't want a pub with no fire, do you?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f73284038ae50028d028ab", "m_prefabName": "MA_Tavern_RoofWindowBig", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_RoofWindowBig", "m_displayName": "Tavern Sleeping Quarters", "m_description": "Half of a tavern's income would come from travellers wanting a bed for the night.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64a41f2117b2ee00280797ac", "m_prefabName": "MA_Tavern_Sign", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern:Actions", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern/MA_Tavern_Sign", "m_displayName": "Tavern Sign", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionTavern|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853bff799fd9031f6a5f67", "m_prefabName": "MA_Tavern_Swamp_Door", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Swamp :Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_Door", "m_displayName": "SwampTavern Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853c0bcba7ed02fb856c9e", "m_prefabName": "MA_Tavern_Swamp_RoofSide", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_RoofSide", "m_displayName": "SwampTavern RoofSide", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853c10cba7ed02fb856cc0", "m_prefabName": "MA_Tavern_Swamp_RoofTop", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Dispatch Swamp :Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_RoofTop", "m_displayName": "SwampTavern RoorTop", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853c15799fd9031f6a5f95", "m_prefabName": "MA_Tavern_Swamp_Sign", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Swamp :Actions", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_Sign", "m_displayName": "SwampTavern Sign", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionTavern|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853c71799fd9031f6a6035", "m_prefabName": "MA_Tavern_Swamp_Wndow", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Swamp :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_Wndow", "m_displayName": "SwampTavern Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67853c769a515802f5690d5c", "m_prefabName": "MA_Tavern_Swamp_WndowBig", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Tavern Swamp :Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Tavern_Swamp/MA_Tavern_Swamp_WndowBig", "m_displayName": "SwampTavern WindowBig", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682c8530a9eda80ba4abb187", "m_prefabName": "MA_Turret_Ammo_Boulder", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:<PERSON><PERSON>", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "10.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Ammo_Boulder", "m_displayName": "Cannon Ammo", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Ammo", "m_nutrition": "0.00000", "m_attack": "0.75000", "m_defence": "0.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682c82791d94aa02d641b61f", "m_prefabName": "MA_Turret_AmmoStock", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:<PERSON><PERSON>", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "1.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_AmmoStock", "m_displayName": "Turret Ammo Stock", "m_description": "Supplies ammo to your Turret. Add your desired ammo to this block", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "AmmoStockIn", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67be0349993e8e032d7c2c02", "m_prefabName": "MA_Turret_Beacon1", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Beacon1", "m_displayName": "Beacon Base", "m_description": "temp part 1 for beacon poc", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "67bee70ae5a75902d1be56cf", "m_prefabName": "MA_Turret_Beacon2", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Beacon2", "m_displayName": "Beacon Middle", "m_description": "temp part 2 for beacon poc", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "67bee7750ff3e902e0772ada", "m_prefabName": "MA_Turret_Beacon3", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "45.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Beacon3", "m_displayName": "Beacon Top", "m_description": "temp part 3 for beacon poc", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "Beacon", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "2.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "663cca4d2ce1f80027d04915", "m_prefabName": "MA_<PERSON>rret_Cannon", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:Actions", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Cannon", "m_displayName": "<PERSON><PERSON><PERSON>", "m_description": "You design it, this beauty will blast it at any foul beast foolish enough to get too close.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionTurret", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "682b593695eaeb02f208923a", "m_prefabName": "MA_<PERSON><PERSON><PERSON>_<PERSON>er", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:Shafts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Stacker", "m_displayName": "<PERSON><PERSON><PERSON> Shaft", "m_description": "Increases the Turret reload time ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "TurretCooldownEnhancement", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "663cca1c7f03240027fc701f", "m_prefabName": "MA_Turret_Structure", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:Bases", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Structure", "m_displayName": "Turret Base", "m_description": "Sturdy foundations are needed for the iron hog cannon that sits atop.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66a3aba956c3800027c5b1ce", "m_prefabName": "MA_Turret_Top", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:Shafts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Top", "m_displayName": "<PERSON><PERSON><PERSON> top", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "TurretCooldownEnhancement", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "663cca3fb1d4920026655c1a", "m_prefabName": "MA_Turret_Window", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Turret:Shafts", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": 0, "m_drawOrderPriority": 0, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_MA_Turret/MA_Turret_Window", "m_displayName": "Turret Window", "m_description": "Turret Room for watching stuff get shot", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "TurretCooldownEnhancement", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf21aa10779b027e1426d1", "m_prefabName": "MA_Weaponsmith_Door", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_Door", "m_displayName": "Weaponsmith Door", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Entrance", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf2231ef007b0282816c0b", "m_prefabName": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>ney", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_Roof_Chimney", "m_displayName": "Weapons<PERSON> ", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf231fce62650285555797", "m_prefabName": "MA_Weaponsmith_RoofSide", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_RoofSide", "m_displayName": "Weaponsmith SideRoof", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "<PERSON><PERSON>", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf23346f37fe02843e2990", "m_prefabName": "<PERSON>_<PERSON><PERSON>_R<PERSON>W<PERSON>ow", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_RoofWindow", "m_displayName": "Weaponsmith Room", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf2367aef82d028a932718", "m_prefabName": "<PERSON>_Weaponsmith_Shade", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_Shade", "m_displayName": "Weaponsmith Stall", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionWeaponsmith|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf224ce29c5402862371a7", "m_prefabName": "MA_Weaponsmith_Sign", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_Sign", "m_displayName": "Weaponsmith Sign", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "None", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bf21d61e3b5d028a98909b", "m_prefabName": "MA_Weaponsmith_Window", "m_buildingPartType": "NGShop", "m_rarity": "Rare", "m_nGProductInfo": "", "m_mADrawerInfos": "Weaponsmith:Aesthetics", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_Weaponsmith/MA_Weaponsmith_Window", "m_displayName": "Weaponsmith Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366b201045502d3ccfb03", "m_prefabName": "MA_WorkerHouse_BriarLake_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Action|House Cotton:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Door_A", "m_displayName": "BriarLake Stonhouse DoorA", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366b569d78e031be09961", "m_prefabName": "MA_WorkerHouse_BriarLake_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Action|House Cotton:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Door_B", "m_displayName": "BriarLake Stonehouse DoorB", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366d333fe6c03097c8a76", "m_prefabName": "MA_Worker<PERSON>ouse_<PERSON><PERSON><PERSON><PERSON>ake_Roof_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Roof_A", "m_displayName": "BriarLake Stonhouse RoofA", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366c869d78e031be099be", "m_prefabName": "MA_Worker<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>ake_Roof_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Roof_B", "m_displayName": "BriarLake Stonhouse ChimneyRoof", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366c533fe6c03097c8a42", "m_prefabName": "MA_WorkerHouse_BriarLake_Window_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Window_A", "m_displayName": "BriarLake Stonhouse WindowA", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366be01045502d3ccfb17", "m_prefabName": "MA_WorkerHouse_BriarLake_Window_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Window_B", "m_displayName": "BriarLake Stonhouse WindowB", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366ba69d78e031be0997c", "m_prefabName": "MA_WorkerHouse_BriarLake_Window_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Window_C", "m_displayName": "BriarLake Stonhouse WindowC", "m_description": "tbc", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67b366b801045502d3ccfb09", "m_prefabName": "MA_WorkerHouse_BriarLake_Window_D", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_BriarLake/MA_WorkerHouse_BriarLake_Window_D", "m_displayName": "BriarLake  Stonhouse WindowD", "m_description": "tbc\n", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f5fa665cf1d60029ed6c03", "m_prefabName": "MA_WorkerHouse_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Cotton:Action|House:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Door_A", "m_displayName": "Rustic Cottage Door", "m_description": "The kind of door you can leave on the latch. The kind of door that your cow might nudge open with her big, pink nose, snuffling around for a little pat on the head and a kind word.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f604abe5ccc900281f12ba", "m_prefabName": "MA_WorkerHouse_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Action|House:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Door_B", "m_displayName": "Weatherworn Cottage Door", "m_description": "Part of the charm of wood is the way it ages and weathers, and this is a charming door to say the least.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f64e4744e66600295bee12", "m_prefabName": "MA_WorkerHouse_Door_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Action|House:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Door_C", "m_displayName": "Pastoral Cottage Door", "m_description": "It's simple, it's plain, and it's eminently pastoral. ", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f64e8344e66600295c0e21", "m_prefabName": "MA_WorkerHouse_Door_D", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Action|House:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Door_D", "m_displayName": "Farmhouse Cottage Door", "m_description": "This is the door they talk about when they ask if you were born in a barn.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f64e9ce5ccc9002822fdc8", "m_prefabName": "MA_WorkerHouse_Door_E", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Action|House:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.00", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Door_E", "m_displayName": "Folksy Cottage Door", "m_description": "When you look at this door, who do you imagine is on the other side?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d4dc915392800265b143b", "m_prefabName": "MA_WorkerHouse_Metal_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Action|House Metal:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.00", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Door_A", "m_displayName": "MA_WorkerHouse_Metal_Door_A", "m_description": "MA_WorkerHouse_Metal_Door_A", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d581789f390002875f611", "m_prefabName": "MA_WorkerHouse_Metal_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Action|House Metal:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.00", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Door_B", "m_displayName": "MA_WorkerHouse_Metal_Door_B", "m_description": "MA_WorkerHouse_Metal_Door_B", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5abc97cd02002867a78a", "m_prefabName": "MA_WorkerHouse_Metal_Door_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.00", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Door_C", "m_displayName": "MA_WorkerHouse_Metal_Door_C", "m_description": "MA_WorkerHouse_Metal_Door_C", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.35000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5ad0b515d60027bd7468", "m_prefabName": "MA_WorkerHouse_Metal_Roof_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Roof_A", "m_displayName": "MA_WorkerHouse_Metal_Roof_A", "m_description": "MA_WorkerHouse_Metal_Roof_A", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5aea1ce5dd002abf3af2", "m_prefabName": "MA_WorkerHouse_Metal_Roof_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Roof_B", "m_displayName": "MA_WorkerHouse_Metal_Roof_B", "m_description": "MA_WorkerHouse_Metal_Roof_B", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5afbee4eb8002648c41f", "m_prefabName": "MA_WorkerHouse_Metal_Roof_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Roof_C", "m_displayName": "MA_WorkerHouse_Metal_Roof_C", "m_description": "MA_WorkerHouse_Metal_Roof_C", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5b13cc36dd0029e0cd2f", "m_prefabName": "MA_WorkerHouse_Metal_Roof_D", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Roof_D", "m_displayName": "MA_WorkerHouse_Metal_Roof_D", "m_description": "MA_WorkerHouse_Metal_Roof_D\n", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5b53ee4eb8002648c660", "m_prefabName": "MA_WorkerHouse_Metal_Shade", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Shade", "m_displayName": "MA_WorkerHouse_Metal_Shade", "m_description": "MA_WorkerHouse_Metal_Shade", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5b2717e5970029dae091", "m_prefabName": "MA_WorkerHouse_Metal_Window_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Window_A", "m_displayName": "MA_WorkerHouse_Metal_Window_A", "m_description": "MA_WorkerHouse_Metal_Window_A", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "660d5b404f1d14002516afa2", "m_prefabName": "MA_WorkerHouse_Metal_Window_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House Metal:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Metal/MA_WorkerHouse_Metal_Window_B", "m_displayName": "MA_WorkerHouse_Metal_Window_B", "m_description": "MA_WorkerHouse_Metal_Window_B", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6511a170c380027debe07", "m_prefabName": "MA_WorkerHouse_RoofTileTop_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofTileTop_A", "m_displayName": "Blue Slate Cottage Roof", "m_description": "Beautiful tiles come rain or shine.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6513544e66600295c0fc0", "m_prefabName": "MA_WorkerHouse_RoofTileTop_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofTileTop_B", "m_displayName": "Shabby Slate Cottage Roof", "m_description": "Avoid the tedium of uniformity, embrace the shabby chic glory of the higgledy-piggledy!", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f65147038ae50028c860ab", "m_prefabName": "MA_WorkerHouse_RoofTileTop_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofTileTop_C", "m_displayName": "Ragged Cottage Slate Roof", "m_description": "Keeps the rain out, not that good at keeping the heat in, though…", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f65178a4f5280029fc3158", "m_prefabName": "MA_WorkerHouse_RoofTileTopChimney_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofTileTopChimney_A", "m_displayName": "Rustic Cottage Slate Roof & Chimney", "m_description": "The invention of the chimney changed the inside of the Albion hovel from smoke filled hell hole to warming, welcoming, dingy dark cavern.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f65187170c380027debe89", "m_prefabName": "MA_WorkerHouse_RoofTileTopChimney_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofTileTopChimney_B", "m_displayName": "Pastoral Slate Cottage Roof & Chimney", "m_description": "They don't let any old busybody have a roof like this, oh no they don't.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6ee7b1438cf0026d5b4e6", "m_prefabName": "MA_WorkerHouse_RoofWindow_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofWindow_A", "m_displayName": "Cottage Roof & Window", "m_description": "A delightful spot for mooching about and admiring the views on a sunny Sunday afternoon.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6ee8ae5ccc90028282e37", "m_prefabName": "MA_WorkerHouse_RoofWindow_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_RoofWindow_B", "m_displayName": "Rustic Cottage Roof & Window", "m_description": "They say that if you look close enough, you can see the capital from up here.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6ee9a170c380027e1bf29", "m_prefabName": "MA_WorkerHouse_Shade", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Shade", "m_displayName": "Rustic Cottage Shade", "m_description": "A sweet spot to duck out from the glaring sunshine.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6eeb961be68002704250b", "m_prefabName": "MA_WorkerHouse_ShadeThatched", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_ShadeThatched", "m_displayName": "Thatched Cottage Shade", "m_description": "The thatch in this shade is so local it's grown within the walls of Oakridge.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6eecc4324000026634046", "m_prefabName": "MA_WorkerHouse_SideWindow", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_SideWindow", "m_displayName": "Cottage Nook Window", "m_description": "A lovely little peephole for spying on your neighbours.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6751be68bc79ef02d6637db7", "m_prefabName": "MA_WorkerHouse_Swamp_Door_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Action|House: Swamp:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Door_A", "m_displayName": "Swamp House DoorA", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6751bdbfbc79ef02d663799c", "m_prefabName": "MA_WorkerHouse_Swamp_Door_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Action|House: Swamp:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Door_B", "m_displayName": "Swamp House DoorB", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Drag|Entrance|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6751be7ced9bed02cd477df9", "m_prefabName": "MA_WorkerHouse_Swamp_Door_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Action|House: Swamp:Entrances", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Door_C", "m_displayName": "Swamp House DoorC", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Entrance", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6751be9aed9bed02cd477e1c", "m_prefabName": "MA_WorkerHouse_Swamp_Roof_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Roof_A", "m_displayName": "Swamp House RoorA", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6751beb307249803237832e3", "m_prefabName": "MA_WorkerHouse_Swamp_Roof_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Roof_B", "m_displayName": "Swamp House RoorB", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "ActionHouse|Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6787d52b6d2c7f02cb832e7b", "m_prefabName": "MA_WorkerHouse_Swamp_Roof_C", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Roof_C", "m_displayName": "Swamp House RoorC", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6787d532754d4b02cf02094a", "m_prefabName": "MA_WorkerHouse_Swamp_Shade", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Aesthetic", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Shade", "m_displayName": "Swamp House Shade", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Aesthetic", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6787d53a754d4b02cf020997", "m_prefabName": "MA_WorkerHouse_Swamp_Window", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House: Swamp:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse_Swamp/MA_WorkerHouse_Swamp_Window", "m_displayName": "Swamp House Window", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6eee52adda800288801e7", "m_prefabName": "MA_WorkerHouse_ThatchedRoofChimney", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_ThatchedRoofChimney", "m_displayName": "Chocolate Box Thatched Cottage Roof & Chimney", "m_description": "Remember to clean out your chimney, hot coals will set fire to this roof quick as a flash.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Chimney|Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6eef8a4f5280029ff9bea", "m_prefabName": "MA_WorkerHouse_ThatchedRoofTop", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Roofs", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_ThatchedRoofTop", "m_displayName": "Picturesque Thatched Cottage Roof", "m_description": "This roof has featured in a variety of celebrated landscape paintings.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Drag|Tap", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6ef0c170c380027e1c1e9", "m_prefabName": "MA_WorkerHouse_Window_A", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Window_A", "m_displayName": "Worker's Cottage Window", "m_description": "Look inside and you will see, a worn out worker, having a pee.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "64f6ef1b038ae50028cd7fa1", "m_prefabName": "MA_WorkerHouse_Window_B", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "House:Bedrooms", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "60.0", "m_numTapsToMake": "50.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "", "m_tableScale": "", "m_prefabPath": "_CurrentlyUsed/_MA_WorkerHouse/MA_WorkerHouse_Window_B", "m_displayName": "Common Worker House Window", "m_description": "Is it the window that's common, or the worker?", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "Bedroom", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.07500", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67dae33d22155a031d41501d", "m_prefabName": "QuestTableMetal", "m_buildingPartType": "NGHouse", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "", "m_sellingPrice": "0.00", "m_price": "0.00", "m_workerTimeToMake": "1.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "<PERSON><PERSON>", "m_productMaterials": "RawMaterialAny", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "1.00", "m_prefabPath": "_CurrentlyUsed/Quest/QuestTableMetal", "m_displayName": "Quest Table Metal", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "BCActionQuestOrder|QuestStockIn", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "True", "m_m_dontAllowDelete": ""}, {"id": "683dd3ad4d8a6e031c9b5215", "m_prefabName": "StockRequiredBoard", "m_buildingPartType": "NGFactory", "m_rarity": "Common", "m_nGProductInfo": "", "m_mADrawerInfos": "Factory:Stock|Farm Cotton:Action|Farm:Stock|Lumber Mill:Stock|Metal Mine:Stock|Metal Smelter:Stock|Mill Produce:Stock", "m_sellingPrice": "1.00", "m_price": "0.00", "m_workerTimeToMake": "14.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "RawResourceClay", "m_starterPack": "True", "m_drawer": 2, "m_drawOrderPriority": 5, "m_drawerScale": "0.00", "m_tableScale": "0.00", "m_prefabPath": "_CurrentlyUsed/_Civic/Civic_StockRequiredBoard", "m_displayName": "Stock Information Board", "m_description": "Handy if you need to see stock your building is waiting for.", "m_usageCap": 1, "m_usesModifier": "1.00", "m_components": "GUIOrderInfo", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "50.00000", "m_beauty": "0.00020", "m_warmth": "0.00000", "m_buildingRaise": "0.00", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb7b2c61e1c002751fdfc", "m_prefabName": "Weapon_AxeHead_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "41.25", "m_price": "30.95", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_A", "m_displayName": "Bloody Mary", "m_description": "Hack it into the faces of your enemies, the open flat of this axe head means that when you tear it from their features, you're guaranteed to pull great arcs of blood from the gore-fest hole.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb94c3b64e300270c202a", "m_prefabName": "Weapon_AxeHead_B", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "41.50", "m_price": "31.15", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_B", "m_displayName": "Crescent Head", "m_description": "Put 'em to sleep with the brutal <PERSON> Head. Sweet dreams…", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb976090e560029420825", "m_prefabName": "Weapon_AxeHead_C", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "80.75", "m_price": "60.55", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "7.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_C", "m_displayName": "War Axe", "m_description": "Swift and simple. An axe head for the warrior who has no time for fancy or flash. Swing and a hit, rinse and repeat.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.80000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66915ad145037a00268f40ca", "m_prefabName": "Weapon_AxeHead_D", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "53.10", "m_price": "39.85", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_D", "m_displayName": "<PERSON> Chopper", "m_description": "Small, unfussy, easy to swing, can take an arm off in the hands of a violent maniac. You have been warned.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.60000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66915aed63ed950027c60337", "m_prefabName": "Weapon_AxeHead_E", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "53.25", "m_price": "39.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_E", "m_displayName": "Hatchet", "m_description": "It's for burying. In heads, bowels, buttocks, anywhere you fancy, really.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.60000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66915b0745037a00268f40e9", "m_prefabName": "Weapon_AxeHead_F", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "53.60", "m_price": "40.20", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_F", "m_displayName": "Old Faithful", "m_description": "It won't let you down. And if it does, who cares, you'll be dead.\n\nNo refunds.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.60000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6694f46d8e99da00256fcabb", "m_prefabName": "Weapon_AxeHead_G", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Axes", "m_sellingPrice": "65.50", "m_price": "49.10", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeHead_G", "m_displayName": "Sunset Slicer", "m_description": "Sharpened on the hooves of free range goats, this axe head will bring the night to any fool that gets on its wrong side.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.70000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb99935d5a20027dcff65", "m_prefabName": "Weapon_AxeShaft_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "31.20", "m_price": "23.40", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_A", "m_displayName": "Ornate War Shaft", "m_description": "when the going gets tough", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb9be7fc95c0027cdbe28", "m_prefabName": "Weapon_AxeShaft_B", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "70.30", "m_price": "52.70", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_B", "m_displayName": "Square Battle Shaft", "m_description": "when you need more purchase", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "668fb9ec83d24000292e4f88", "m_prefabName": "Weapon_AxeShaft_C", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "31.25", "m_price": "23.40", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_C", "m_displayName": "Rounded War Shaft", "m_description": "For the discerning warrior", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6694f490e8bd62002837f014", "m_prefabName": "Weapon_AxeShaft_D", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "43.25", "m_price": "32.40", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_D", "m_displayName": "Heavy Pointed War Shaft", "m_description": "B<PERSON><PERSON>", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6694f4ac2eb0c60028b15a05", "m_prefabName": "Weapon_AxeShaft_E", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "55.00", "m_price": "41.25", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_E", "m_displayName": "Heavy Regal Warshaft", "m_description": "Elegant", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6694f4d3e5f1820028f584e1", "m_prefabName": "Weapon_AxeShaft_F", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "70.60", "m_price": "52.95", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_AxeShaft_F", "m_displayName": "Heavy Square Warshaft", "m_description": "Powerful", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661914b1e0d57f0028fa615b", "m_prefabName": "Weapon_Blade_BrowdSword_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "41.25", "m_price": "30.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_BrowdSword_A", "m_displayName": "Steel Broadsword Blade", "m_description": "The longer blade is a beast in the right hands. Careful who you arm with this, it is not for the novice swordsmith. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661914d086c5530028da2024", "m_prefabName": "Weapon_Blade_Cutlass_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "41.50", "m_price": "31.15", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_Cutlass_A", "m_displayName": "Curved Steel Blade", "m_description": "The single edged blade is a popular choice across the globe. This is no pointy stick, this is a full on hack and slash horror show.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661914e2f150df0028521bf5", "m_prefabName": "Weapon_Blade_Dagger_A", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "43.75", "m_price": "32.80", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_Dagger_A", "m_displayName": "Short Curved Steel Blade", "m_description": "It's for plunging. \n\nPlunge it into guts, plunge it into necks, plunge it into anything soft and fleshy. ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661914fe72a9d80029c35bf8", "m_prefabName": "Weapon_Blade_Katana_A", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "80.00", "m_price": "60.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "7.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_Katana_A", "m_displayName": "Curved Folded Steel Blade", "m_description": "Thinner than it's flashy cousin, the speed one can wield this with is a delight. You'll have both your enemy's arms off before they can raise their own feeble weapon.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.80000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619151077ea88002812d8a0", "m_prefabName": "Weapon_Blade_Sword_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "41.00", "m_price": "30.75", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_Sword_A", "m_displayName": "Medium Double Edged Straight Steel Blade", "m_description": "Bog standard sword design, you might think. But there's a reason this is a blade used the world over. It's effective on the battlefield, in small spaces, and the holder needs little training in the art of steel slaughter.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619151db481540026141ee6", "m_prefabName": "Weapon_Blade_Sword_B", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Blades", "m_sellingPrice": "65.55", "m_price": "49.15", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Blade_Sword_B", "m_displayName": "Gladius Blade", "m_description": "Short in stature, with a variable width, the Gladius blade is a double-edged length of steel that is as versatile as it is vicious. So many ways to cut, thrust and slice…", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.70000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67af26a2ae4f8d031318a8b3", "m_prefabName": "Weapon_Bow_TEST", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "", "m_price": "", "m_workerTimeToMake": "30.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Bow_TEST", "m_displayName": "Weapon_Bow_TEST", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.01000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "67d2ccd4a1e24902c782b65f", "m_prefabName": "Weapon_CrossBow_TEST", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "", "m_price": "", "m_workerTimeToMake": "30.0", "m_numTapsToMake": "1.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.50", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_CrossBow_TEST", "m_displayName": "Weapon_CrossBow_TEST", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.01000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66decf2d9c959402e604ee59", "m_prefabName": "Weapon_Hammer_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Hammers", "m_sellingPrice": "41.35", "m_price": "31.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hammer_A", "m_displayName": "Face Smasher", "m_description": "Had enough of teeth? Have we got just the thing!", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66decf4d74068802c537b487", "m_prefabName": "Weapon_Hammer_B", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Hammers", "m_sellingPrice": "41.20", "m_price": "30.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hammer_B", "m_displayName": "Mind Boggler", "m_description": "It goes in one ear, it comes clean through to the other. Say goodbye to your enemies bad thoughts! ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66decf7174068802c537b803", "m_prefabName": "Weapon_Hammer_C", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Hammers", "m_sellingPrice": "53.70", "m_price": "40.30", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hammer_C", "m_displayName": "The Lethal Lump", "m_description": "One big lump of very heavy metal. Folds faces in on themselves. 100% guaranteed.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.60000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66decf8a9c959402e604f450", "m_prefabName": "Weapon_Hammer_D", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Hammers", "m_sellingPrice": "65.50", "m_price": "49.15", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hammer_D", "m_displayName": "The Sophisticunt", "m_description": "A fabulous weapon for those occasions when your opponent is waxing lyrical about their abilities on the field of combat. <PERSON> served hard and fast. Right in the smacker.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.70000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66decfa28a664d03093d5b30", "m_prefabName": "Weapon_Hammer_E", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Hammers", "m_sellingPrice": "80.30", "m_price": "60.25", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "7.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hammer_E", "m_displayName": "The Crooked Claw", "m_description": "Take your pick of the pain parts with this beaut. Smack 'em with the flat end for hefty damage, or swing the clawed side at 'em and gash your way to the soft bits inside.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.80000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66191533e589820028635817", "m_prefabName": "Weapon_Handle_A", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "31.25", "m_price": "23.45", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_A", "m_displayName": "One-handed Grip", "m_description": "The perfect length for a one hander. Fits like a velvet glove.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619154c72a9d80029c35ce8", "m_prefabName": "Weapon_Handle_B", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "21.10", "m_price": "15.80", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_B", "m_displayName": "Bastard Grip", "m_description": "This is not a hilt for those born out of wedlock, rather it is for anyone who likes to fight with more than one hand on their sword, but less than two.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661915568f5bdd00284648d0", "m_prefabName": "Weapon_Handle_C", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "60.50", "m_price": "45.40", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_C", "m_displayName": "Hand-and-a-half Grip", "m_description": "It's all in the wrist. And the extra fingers you can wrap round it.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.40000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66191560f150df0028521e80", "m_prefabName": "Weapon_Handle_D", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "21.35", "m_price": "16.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_D", "m_displayName": "Two-hand Grip", "m_description": "You'll certainly need one of these if you've a blade that's big enough.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661915d372a9d80029c35fca", "m_prefabName": "Weapon_Handle_E", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "45.25", "m_price": "34.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_E", "m_displayName": "Claymore Grip", "m_description": "If you're designing a sword for a lunatic who wants to rush onto the field of combat and scream bloody murder while waving about a sword that's bigger than a castle, this is the grip for you.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.30000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661915e2487f6e0026651ecf", "m_prefabName": "Weapon_Handle_F", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Grips", "m_sellingPrice": "21.10", "m_price": "15.85", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Handle_F", "m_displayName": "Longsword Grip", "m_description": "You wouldn't design a 3 storey building with a staircase that only reached the 2nd floor, and neither should you design a long sword without a grip of this magnitude.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661915f4ab6ee200290f958c", "m_prefabName": "Weapon_Hilt_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Crossguards", "m_sellingPrice": "21.70", "m_price": "16.30", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hilt_A", "m_displayName": "Whimsical Curved Crossguard", "m_description": "Style over substance? There's only one way to find out…", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619160372a9d80029c36259", "m_prefabName": "Weapon_Hilt_B", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Crossguards", "m_sellingPrice": "13.50", "m_price": "10.15", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hilt_B", "m_displayName": "<PERSON><PERSON><PERSON> Capped Hilt", "m_description": "This is not a guard for the fist, it is an extra weapon to add to your arsenal. One punch with this over your knuckles and your enemy will have a bloody nose and a date with death.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661916128f5bdd0028464d6a", "m_prefabName": "Weapon_Hilt_C", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Crossguards", "m_sellingPrice": "11.95", "m_price": "9.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hilt_C", "m_displayName": "S-Tapered Short Guard", "m_description": "Technique! Technique! It's all in the technique! And this one's a doozy. Get it wrong, you'll lose a hand. Get it right, you'll disarm your enemy and have them grovelling at your feet.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619161f77ea88002812dc93", "m_prefabName": "Weapon_Hilt_D", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Crossguards", "m_sellingPrice": "40.00", "m_price": "30.00", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hilt_D", "m_displayName": "Short Disc Hilt", "m_description": "No nonsense hilts are for no nonsense blades. Forget about the frills and the fancy, think only of the kill.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619162b114402002734f797", "m_prefabName": "Weapon_Hilt_E", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Crossguards", "m_sellingPrice": "35.75", "m_price": "26.80", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Hilt_E", "m_displayName": "<PERSON> Breaker", "m_description": "One wrong move from your opponent and their blade will snap or be wrenched from their grip. It takes a deft touch and a strong wrist to use this guard to maximum effect.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb3d614509c700332f33de", "m_prefabName": "Weapon_Mace_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "41.80", "m_price": "31.35", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Mace_A", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb3d8bdb9d290032620267", "m_prefabName": "Weapon_Mace_B", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "53.60", "m_price": "40.20", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "5.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Mace_B", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.60000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb3dc1d8c6a800315b2886", "m_prefabName": "Weapon_Mace_C", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "41.65", "m_price": "31.25", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "4.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Mace_C", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.50000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb3e64ce6a470031cac127", "m_prefabName": "Weapon_Mace_D", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "65.20", "m_price": "48.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "6.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Mace_D", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.70000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb5b72e2817d003339dca4", "m_prefabName": "Weapon_MaceShaft_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "11.85", "m_price": "8.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_MaceShaft_A", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb5b914cc1960032f8d1fe", "m_prefabName": "Weapon_MaceShaft_B", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "23.00", "m_price": "17.25", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_MaceShaft_B", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb5baed1f61a00312db4c4", "m_prefabName": "Weapon_MaceShaft_C", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "11.60", "m_price": "8.70", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_MaceShaft_C", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb5bfbee58870032e44da3", "m_prefabName": "Weapon_MaceShaft_D", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "35.95", "m_price": "26.95", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_MaceShaft_D", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66bb5c23fc6fda003183ec9f", "m_prefabName": "Weapon_MaceShaft_E", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Maces", "m_sellingPrice": "40.25", "m_price": "30.20", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_MaceShaft_E", "m_displayName": "", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661916474f04a40027636e6f", "m_prefabName": "Weapon_Pummel_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:<PERSON><PERSON><PERSON>", "m_sellingPrice": "11.90", "m_price": "8.90", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Pummel_A", "m_displayName": "White Knight Pommel", "m_description": "When dressing a sword, one should always ensure it's obvious what side of the good and evil coin its owner resides… ", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "6619165ef150df0028522437", "m_prefabName": "Weapon_Pummel_B", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:<PERSON><PERSON><PERSON>", "m_sellingPrice": "25.80", "m_price": "19.35", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Pummel_B", "m_displayName": "The Bonker", "m_description": "Pop this pommel onto the base of your swords if you are arming the sort of fighters who like to bonk their enemies in the eye with a quick yet deadly jab of the wrong end.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66191667b5e03c00267e5885", "m_prefabName": "Weapon_Pummel_C", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:<PERSON><PERSON><PERSON>", "m_sellingPrice": "13.75", "m_price": "10.30", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Pummel_C", "m_displayName": "The Catcher", "m_description": "Are your troops a little bit flimsy in the grip department? Pop one of these pommels on the base of their swords and stop fretting about them dropping their weapons at the first sign of trouble.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "661916718f5bdd002846503b", "m_prefabName": "Weapon_Pummel_D", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:<PERSON><PERSON><PERSON>", "m_sellingPrice": "30.25", "m_price": "22.70", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "True", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "1.00", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_Pummel_D", "m_displayName": "The Flat Hander", "m_description": "It won't win any design awards, but if it's function over form you're after, the Flat Hander is your go to pommel piece.", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "65969c379c53b70028d700d3", "m_prefabName": "Weapons_Hilt_Knight", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "", "m_sellingPrice": "11.65", "m_price": "8.75", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "Metal", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.70", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapons_Hilt_Knight", "m_displayName": "Poor Weapon Cross Guard", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b0a7f343594800294cb055", "m_prefabName": "Weapon_WoodGrip_A", "m_buildingPartType": "", "m_rarity": "Common", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "11.50", "m_price": "8.65", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "1.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON>", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_WoodGrip_A", "m_displayName": "A Simple Wooden Shaft", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.05000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b0a81d1682a600272b0ad6", "m_prefabName": "Weapon_WoodGrip_B", "m_buildingPartType": "", "m_rarity": "Rare", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "23.15", "m_price": "17.35", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "2.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON>", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_WoodGrip_B", "m_displayName": "A Simple Wooden Shaft", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.10000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b0a85da0f8980028feb4b0", "m_prefabName": "Weapon_WoodGrip_C", "m_buildingPartType": "", "m_rarity": "Epic", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "35.00", "m_price": "26.25", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON>", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_WoodGrip_C", "m_displayName": "A Simple Wooden Shaft", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}, {"id": "66b0a96d1682a600272b1926", "m_prefabName": "Weapon_WoodGrip_D", "m_buildingPartType": "", "m_rarity": "Legendary", "m_nGProductInfo": "Weapons", "m_mADrawerInfos": "Weapons:Shafts", "m_sellingPrice": "40.25", "m_price": "30.20", "m_workerTimeToMake": "20.0", "m_numTapsToMake": "30.0", "m_materialCost": "3.0", "m_buildingMaterials": "RawResourceClay", "m_productMaterials": "<PERSON><PERSON>", "m_starterPack": "False", "m_drawer": "", "m_drawOrderPriority": "", "m_drawerScale": "0.75", "m_tableScale": "2.00", "m_prefabPath": "_CurrentlyUsed/_MA_Weapons/Weapon_WoodGrip_D", "m_displayName": "A Simple Wooden Shaft", "m_description": "", "m_usageCap": 1, "m_usesModifier": "1.50", "m_components": "", "m_nutrition": "0.00000", "m_attack": "0.20000", "m_defence": "0.00000", "m_beauty": "0.00000", "m_warmth": "0.00000", "m_buildingRaise": "", "m_extentType": "Normal", "m_isInvisible": "False", "m_hideFromUser": "False", "m_m_dontAllowDelete": ""}]