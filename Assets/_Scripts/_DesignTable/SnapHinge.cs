using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SnapHinge : MonoBehaviour, IBatchPartitioner
{

	// ARTISTS: To add more categories, write them in THIS structure!
	// --------------------------------------------------------------------
	public enum ECategory
	{
		Default = 0,
		Main,
		Decoration,
		Special,
		LastChoice,
		InheritVisuals, // inherits visuals from an InheritVisualsSource
		InheritVisualsSource, // can be used by InheritVisuals
		// Clothes
		ClothesDecoration, // must match generic (pre-ClothesDecoration) types
		// must match in pairs
		ClothesTargetHead,
		ClothesSourceHead,
		ClothesTargetChest,
		ClothesSourceChest,
		ClothesTargetHips,
		ClothesSourceHips,
		ClothesTargetLegs,
		ClothesSourceLegs,
		ClothesTargetArms,
		ClothesSourceArms,
		ClothesTargetHands,
		ClothesSourceHands,
		ClothesTargetFeet,
		ClothesSourceFeet,
		ClothesTargetBelt,
		ClothesSourceBelt,
		//
		ClothesPairs = ClothesTargetHead,
		//
		PostClothes = ClothesDecoration + 50, // leave room for more clothes!
		//
		Beacon,
	}
	// --------------------------------------------------------------------


	// DO NOT CHANGE THE ORDER OF THIS ENUM, THINGS WILL BREAK
	public enum EType
	{
		None = 0,
		Left,
		Right,
		Top,
		Bottom,
        Front,
        Back,
        LAST,
	}

    public static Dictionary<EType, EType> OppositeHinge = new Dictionary<EType, EType>
    {
        {EType.None, EType.None},
        {EType.Left, EType.Right},
        {EType.Right, EType.Left},
        {EType.Top, EType.Bottom},
        {EType.Bottom, EType.Top},
        {EType.Front, EType.Back},
        {EType.Back, EType.Front},
    };

	public ECategory HingeCategory { get { return m_hingeCategory; } }
	public EType HingeDirection { get { return m_hingeDirectionType; } }
	public bool Mirrorable { get { return m_mirrorable; } }
	
	public bool IsGeneric => m_hingeCategory < ECategory.ClothesDecoration;
	public bool IsClothesDecoration => m_hingeCategory == ECategory.ClothesDecoration;
	public bool IsClothesPaired => m_hingeCategory > ECategory.ClothesDecoration;
	public bool IsClothesSource => IsClothesPaired && ((m_hingeCategory - ECategory.ClothesPairs) & 1) == 1;
	public bool IsClothesTarget => IsClothesPaired && ((m_hingeCategory - ECategory.ClothesPairs) & 1) == 0;
	public bool IsBeacon => m_hingeCategory == ECategory.Beacon;
	public bool m_isAmmo = false;
	
	public bool CanSnapTo(SnapHinge _other)
	{
		if (IsBeacon != _other.IsBeacon) return false;
		bool isThisGeneric = IsGeneric || IsBeacon, isThisClothesDecoration = IsClothesDecoration;
		bool isOtherGeneric = _other.IsGeneric || IsBeacon, isOtherClothesDecoration = _other.IsClothesDecoration;
		if (isThisGeneric && isOtherGeneric)
			return true; // generic types always pair
		if (isThisClothesDecoration)
			return isOtherGeneric; // clothes decoration must pair with generic 
		if (isOtherClothesDecoration)
			return isThisGeneric; // clothes decoration must pair with generic
		if (isThisGeneric || isOtherGeneric) // the only remaining cases are one generic one clothes or two clothes
			return false; // generic and clothes don't pair
		// two clothes, match if paired
		int thisClothesType = (int)m_hingeCategory - (int)ECategory.ClothesPairs;
		int otherClothesType = (int)_other.m_hingeCategory - (int)ECategory.ClothesPairs;
		return (thisClothesType ^ otherClothesType) == 1;
	}
	
	public string m_inheritTag;
	[System.Serializable]
	public class MaterialTag
	{
		public string m_name;
		public Material m_material;
	}
	public MaterialTag[] m_materialTags;
	public Material GetMaterial(string _name)
	{
		if (m_materialTags == null || m_materialTags.Length == 0) return null;
		foreach (MaterialTag tag in m_materialTags)
		{
			if (tag.m_name == _name)
				return tag.m_material;
		}
		return m_materialTags[0].m_material;
	}

	// -- private --
	[SerializeField]private ECategory m_hingeCategory;
	[SerializeField]private EType m_hingeDirectionType;
	[SerializeField]private bool m_mirrorable = false;
	private List<EDesignFitnessType> m_requiredAttachTypes = new List<EDesignFitnessType>();
	public List<EDesignFitnessType> RequiredAttatchTypes => m_requiredAttachTypes;

	public void Start()
	{
		DesignHingeType fitnessTypes = transform.GetComponentInChildren<DesignHingeType>();
		if(fitnessTypes != null)
			m_requiredAttachTypes = fitnessTypes.RequiredAttachTypes;
	}
	
    public void SetHingeDirectionType(EType _hingeDirectionType)
    {
        m_hingeDirectionType = _hingeDirectionType;
    }

	public EType GetHingeDirectionType() {
		if (transform.lossyScale.x * transform.lossyScale.y * transform.lossyScale.z < 0) // GL - 281118 - if we've flipped this item flip its hinges too
			return OppositeHinge[HingeDirection];
		return HingeDirection;
	}


#if UNITY_EDITOR
	private static string MeshLocation = "Assets/_Art/DesignScene/Product_Attach_Points.fbx";
	private static Mesh AttachPointMesh = null;
	private static Vector3 gizmoScale = Vector3.one * 0.05f;
	private static Quaternion offsetRotation = Quaternion.Euler( 90.0f, 0.0f, 0.0f );

	private void OnDrawGizmosSelected()
	{
		AttachPointMesh = AttachPointMesh ?? UnityEditor.AssetDatabase.LoadAssetAtPath( MeshLocation, typeof(Mesh) ) as Mesh;
		Gizmos.color = Color.yellow;
		Gizmos.DrawMesh( AttachPointMesh, this.transform.position, this.transform.rotation * offsetRotation, gizmoScale );
	}
#endif

	public Component Component() => this;

	public List<List<Transform>> GetExcludedTransforms()
	{
		var list = new List<List<Transform>>();
		foreach (var mr in GetComponentsInChildren<MeshRenderer>(true))
			list.Add(new List<Transform> { mr.transform });
		return list;
	}

	public void OnBatch(List<Transform> _batchedOut)
	{
		// nothing to do here
	}
}