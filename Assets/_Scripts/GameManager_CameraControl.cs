using UnityEngine;

public partial class GameManager
{
    public static Vector3 Planecast(Camera cam, Vector2 mousePos, Vector3 focus)
    {
        Ray ray = cam.ScreenPointToRay(mousePos);
        Plane horizontal = new Plane(Vector3.up, new Vector3(0f, focus.y, 0f));
        if (horizontal.Raycast(ray, out float enter))
            return ray.origin + ray.direction * enter;
        return focus;
    }
    
    const float c_cameraControlStageAutoSnapTightness = .04f;
    const float c_zoomSpeedMultiplier = -.3f;
    const float c_rotateSpeedMultiplier = -8;
    const float c_cameraControlStageDelay = .5f;

    private float m_cameraControlStageNextUpdate = 0;
    private float m_cameraControlNextResetAfter = 0;
    private int m_cameraControlStageIgnoreHand = 0;
    
	private bool SmoothZoom => Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
    
	private float m_cameraControlLastDistance = 10, m_cameraControlLastTilt = 30;

	private (float, float) InterpolateCameraControl(float _f)
	{
		int stage = (int)_f;
		float fraction = _f - stage;
		var stagePrevious = MACameraControl.Get(stage);
		var stageNext = MACameraControl.Get(stage + 1);
		return (Mathf.Lerp(stagePrevious.m_height, stageNext.m_height, fraction), Mathf.Lerp(stagePrevious.m_tilt, stageNext.m_tilt, fraction));
	}

	private (float, float) SmoothedCameraControlFromStage()
	{
		var (targetHeight, targetTilt) = InterpolateCameraControl(m_state.m_cameraControlStage);
		var lerp = m_cameraBlendFromT > .001f ? 1 : c_cameraControlStageAutoSnapTightness.TCLerp();
		m_cameraControlLastDistance = Mathf.Lerp(m_cameraControlLastDistance, targetHeight, lerp);
		m_cameraControlLastTilt = Mathf.Lerp(m_cameraControlLastTilt, targetTilt, lerp);
		if (m_cameraControlStageIgnoreHand > 0 && m_cameraControlLastDistance.Nearly(targetHeight, .1f) && m_cameraControlLastTilt.Nearly(targetTilt, .1f))
			--m_cameraControlStageIgnoreHand;
		return (m_cameraControlLastDistance, m_cameraControlLastTilt); 
	}
	
	private bool ToggleMapNewScheme()
	{
		if (m_state.m_cameraControlStage < MACameraControl.NumStages - 1)
			m_state.m_cameraControlStage = MACameraControl.NumStages - 1;
		else
			m_state.m_cameraControlStage = 2;
		return true;
	}

	private float m_cameraControlMinimumZoomLevel = 0;

	private void ClampCameraControlStage()
	{
		m_state.m_cameraControlStage = Mathf.Clamp(m_state.m_cameraControlStage, m_cameraControlMinimumZoomLevel, MACameraControl.NumStages - 1);
	}
	
	public AkEventHolder m_zoomInAudio;
	public AkEventHolder m_zoomOutAudio;

	private void AdjustControlStage(float _deltaZoom, float _dt)
	{
		if (SmoothZoom)
		{
			m_state.m_cameraControlStage += _deltaZoom * _dt * c_zoomSpeedMultiplier * 10;
		}
		else
		{
			var time = Time.time;
			if (time >= m_cameraControlStageNextUpdate)
			{
				int change = 0;
				if (_deltaZoom > 0 && m_state.m_cameraControlStage > m_cameraControlMinimumZoomLevel) change = -1;
				else if (_deltaZoom < 0 && m_state.m_cameraControlStage < MACameraControl.NumStages - 1) change = 1;
				if (change != 0)
				{
					m_state.m_cameraControlStage = (int) m_state.m_cameraControlStage + change;
					m_cameraControlStageNextUpdate = time + c_cameraControlStageDelay;
					if (change < 0) m_zoomInAudio?.Play(gameObject);
					else m_zoomOutAudio?.Play(gameObject);
				}
			}
		}
		ClampCameraControlStage();
	}

	public enum ECameraControlBlendIn
	{
		None, FromNextStage, FromPreviousStage, FromZero, FromCurrentStage
	}

	public void SetCameraControlFromCameraPositionRotation(Vector3 _pos, Vector3 _eulers, ECameraControlBlendIn _blendIn = ECameraControlBlendIn.None)
	{
		var ray = new Ray(_pos, Quaternion.Euler(_eulers) * Vector3.forward);
		SetCameraControlFromRay(ray, _blendIn);
	}

	public Vector3 TerrainPointFromRay(Ray _ray)
	{
		if (Physics.Raycast(_ray, out var hit, 1000, c_layerTerrainBit))
			return hit.point;
		new Plane(Vector3.up, GlobalData.c_seaLevel).Raycast(_ray, out float hitDistance);
		return _ray.GetPoint(hitDistance);
	}

	public void SetCameraControlFromRay(Ray _ray, ECameraControlBlendIn _blendIn = ECameraControlBlendIn.None)
	{
		m_state.m_cameraControlFocus = TerrainPointFromRay(_ray);
		m_state.m_cameraControlDirection = Mathf.Atan2(_ray.direction.x, _ray.direction.z) * Mathf.Rad2Deg;
		m_state.m_cameraControlStage = Mathf.Ceil(MACameraControl.GetStageIndexFromHeight(_ray.origin.y - m_state.m_cameraControlFocus.y));
		SetBlendIn(_blendIn);
	}
	private void SetBlendIn(ECameraControlBlendIn _blendIn)
	{
		m_cameraControlStageIgnoreHand = 3;
		int fromStage = (int)m_state.m_cameraControlStage;
		switch (_blendIn)
		{
			case ECameraControlBlendIn.FromNextStage:
				fromStage += 1;
				break;
			case ECameraControlBlendIn.FromPreviousStage:
				fromStage -= 1;
				break;
			case ECameraControlBlendIn.FromZero:
				fromStage = 0;
				break;
			case ECameraControlBlendIn.None:
				fromStage = (int)m_state.m_cameraControlStage;
				break;
			default:
				return;
		}
		m_cameraControlLastDistance = MACameraControl.Get(fromStage).m_height;
	}

	public void SetCameraControlFromCameraFocusAngle(Vector3 _focus, float _angle, float _stage, ECameraControlBlendIn _blendIn = ECameraControlBlendIn.None)
	{
		m_state.m_cameraControlFocus = _focus;
		m_state.m_cameraControlDirection = _angle;
		m_state.m_cameraControlStage = _stage;
		SetBlendIn(_blendIn);
	}

	public void SetCameraControlFromCameraPosFocus(Vector3 _pos, Vector3 _focus, ECameraControlBlendIn _blendIn = ECameraControlBlendIn.None)
	{
		var ray = new Ray(_pos, _focus - _pos);
		SetCameraControlFromRay(ray, _blendIn);
	}

	private Vector3 m_cameraBlendFromPosition;
	private Quaternion m_cameraBlendFromRotation;
	private float m_cameraBlendFromT = 0;
	private float m_cameraBlendDuration = .5f;
	private System.Action m_cameraBlendFinishCallback = null;
	private void PrepareCameraBlend(float _duration, System.Action _cb)
	{
		var xform = m_camera.transform;
		m_cameraBlendFromPosition = xform.position;
		m_cameraBlendFromRotation = xform.rotation;
		m_cameraBlendFromT = 1;
		m_cameraBlendDuration = _duration;
		m_cameraBlendFinishCallback = _cb;
	}
	public void BlendCameraFromCurrent(float _duration = .5f, System.Action _cb = null)
	{
		var xform = m_camera.transform;
		PrepareCameraBlend(_duration, _cb);
		SetCameraControlFromCameraPositionRotation(xform.position, xform.eulerAngles, ECameraControlBlendIn.None);
	}

	public void BlendCameraFromFocus(Vector3 _focus, float _stage = -1, float _duration = .5f, System.Action _cb = null)
	{
		var xform = m_camera.transform;
		if (_stage < 0) _stage = Mathf.Ceil(MACameraControl.GetStageIndexFromHeight(xform.position.y - _focus.y));
		BlendCameraTo(_focus, xform.eulerAngles.y, _stage, _duration, _cb);
	}

	public void BlendCameraTo(Vector3 _focus, float _direction, float _stage, float _duration = .5f, System.Action _cb = null)
	{
		PrepareCameraBlend(_duration, _cb);
		SetCameraControlFromCameraFocusAngle(_focus, _direction, _stage, ECameraControlBlendIn.None);
	}

	private void UpdateBlendCamera()
	{
		if (m_cameraBlendFromT > 0)
		{
			var t = 1 - m_cameraBlendFromT;
			t = t * t * (3 - t - t);
			var xform = m_camera.transform;
			xform.position = Vector3.Lerp(m_cameraBlendFromPosition, xform.position, t);
			xform.rotation = Quaternion.Slerp(m_cameraBlendFromRotation, xform.rotation, t);
			m_cameraBlendFromT -= Time.deltaTime / m_cameraBlendDuration;
			if (m_cameraBlendFromT < .01f)
			{
				m_cameraBlendFromT = 0;
				if (m_cameraBlendFinishCallback != null)
				{
					var cb = m_cameraBlendFinishCallback;
					m_cameraBlendFinishCallback = null;
					cb();
				}
			}
			m_cameraControlStageIgnoreHand = 3;
		}
	}

	Vector3 GetCameraOffsetFromFocus()
	{
		var camOffset = Vector3.zero;
		var (camDistance, camTilt) = SmoothedCameraControlFromStage();
		camOffset.y = camDistance;
		var camXZOffset = camDistance / Mathf.Tan(camTilt * Mathf.Deg2Rad);
		camOffset.x = -Mathf.Sin(m_state.m_cameraControlDirection * Mathf.Deg2Rad) * camXZOffset;
		camOffset.z = -Mathf.Cos(m_state.m_cameraControlDirection * Mathf.Deg2Rad) * camXZOffset;
		return camOffset;
	}
	
	private void ClampFocus(float _camY)
	{
		const float c_startClampFocus = 200;
		const float c_fullClampFocus = 1200;
		var focus = m_state.m_cameraControlFocus;
		var clampToCenter = Mathf.Clamp01((_camY - c_startClampFocus) / (c_fullClampFocus - c_startClampFocus));
		var min = GlobalData.c_terrainMin;
		var max = GlobalData.c_terrainMax;
		var center = (min + max) * .5f;
		min = Vector3.Lerp(min, center, clampToCenter);
		max = Vector3.Lerp(max, center, clampToCenter);
		focus.x = Mathf.Clamp(focus.x, min.x, max.x);
		focus.z = Mathf.Clamp(focus.z, min.z, max.z);
		m_state.m_cameraControlFocus = focus;
	}
	
	public bool IsCameraBeingOverridden => m_cameraPanSequenceInProgress || m_freeCameraMode || m_cameraTrackingFocus != null || IsPossessing || m_cameraTransition != null || m_cameraBlendFromT > .001f;
	
	private bool FilteredSphereCast(Vector3 _origin, float _radius, Vector3 _direction, out RaycastHit _hit, float _maxDistance, int _layerMask, QueryTriggerInteraction _queryTriggerInteraction)
	{
		var camTransform = m_camera.transform;
		foreach (var hit in Physics.SphereCastAll(_origin, _radius, _direction,  _maxDistance, _layerMask, _queryTriggerInteraction))
		{
			if (hit.transform.IsChildOf(camTransform)) continue;
			_hit = hit;
			return true;
		}
		_hit = default;
		return false;
	}
	private void HandleCollision(Transform _camXform, Vector3 _oldPosition, bool _isZoomingOut)
	{
		var newPosition = _camXform.position;
		var totalDirection = newPosition - _oldPosition;
		var totalDistance = totalDirection.sqrMagnitude;
		if (totalDistance < .0001f * .0001f)
		{
			_camXform.position = _oldPosition;
			return;
		}
		totalDistance = Mathf.Sqrt(totalDistance);
		totalDirection /= totalDistance;
		if (_isZoomingOut)
		{
			// allow any zoom out that doesn't end up in a collision
			if (Physics.OverlapSphere(newPosition, 1, ~c_layerIgnoreCameraBit, QueryTriggerInteraction.Ignore).Length > 0)
				_camXform.position = _oldPosition; // collision, stay still until we zoom further and skip the collider
		}
		else if (FilteredSphereCast(_oldPosition, .2f, totalDirection, out var moveHit, totalDistance + 1, ~c_layerIgnoreCameraBit, QueryTriggerInteraction.Ignore))
			if (moveHit.distance - 1 < totalDistance && Vector3.Dot(totalDirection, moveHit.normal) < 0)
				_camXform.position = _oldPosition + totalDirection * (moveHit.distance - 1);
	}

	private float m_currentZoomChange = 0; public float CurrentZoomChange => m_currentZoomChange;
	private Vector3 m_mouseDragPlaneOrigin;
	private bool UpdateCameraControl(bool _isOverUIScrollable, bool _isOverUIAny)
	{
		var cam = m_camera;
		var camXform = cam.transform;
		
		var oldPosition = camXform.position;

		var isHolding = InputUtilities.GetCurrentDragObject() != null;
		m_cameraControlMinimumZoomLevel = isHolding ? 1 : 0;
		
		if (m_state.m_cameraControlFocus.sqrMagnitude < .01f * .01f)
			m_state.m_cameraControlFocus = RaycastTerrain(.5f, .5f) ?? default;

		var screenReferencePoint = GetDragPosition();
		var hitOld = Planecast(cam, screenReferencePoint, m_state.m_cameraControlFocus);  
		
		float dt = Time.deltaTime;

		var debugMove = DebugCameraMove;
		DebugCameraMove = Vector2.zero;

		bool userKeyboardRotate = false;
		bool userKeyboardMove = false;
		bool mouseIsLegalForMove = !_isOverUIScrollable && IsCountryside;
		bool mouseIsLegalForOther = mouseIsLegalForMove;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		bool dragIsLegal = IsRoadConsuming == false && DesignTableManager.Me.IsDesignGloballyConsuming == false;
		var moveSpeed = DesignTableManager.Me.m_isInDesignGlobally || PlayerHandManager.Me.PowerActive(PlayerHandManager.c_powerTypeCuddle) || BuildingPlacementManager.Consuming ? 0 : c_keySpeedMove;
#else
		bool dragIsLegal = true;
		var moveSpeed = c_keySpeedMove;
#endif
		
		// read inputs
		var zoom = GetZoom(mouseIsLegalForOther, true);
		m_currentZoomChange = zoom;
		zoom += m_zoomBurst * 10;
		zoom += GetMapZoom();
		zoom *= m_cameraControlZoomSpeed * m_cameraControlZoomSpeed;

		float rotate = GetTotalCameraRotate(mouseIsLegalForOther, ref userKeyboardRotate);
		
		var movement = (ReadKeyStick(KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 1), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 3), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 2), KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 0), c_keySpeedMove, ref m_keySmoothMove, ref userKeyboardMove, NGManager.Me.m_moveKeysSmoothing) +
		                ReadKeyStick(KeyCode.LeftArrow, KeyCode.RightArrow, KeyCode.DownArrow, KeyCode.UpArrow, moveSpeed, ref m_keySmoothMove2, ref userKeyboardMove, NGManager.Me.m_moveKeysSmoothing) +
		                debugMove * c_keySpeedMove) * (m_cameraControlMoveSpeed * dt);
		
		movement *= Mathf.Clamp(camXform.position.y * (1f / 100f), 1, 10);

		Vector3 camOffset;
		
		var isBeingOverridden = IsCameraBeingOverridden;
		if (isBeingOverridden == false)
		{
			// dolly
			AdjustControlStage(zoom, dt);

			// rotate
			m_state.m_cameraControlDirection += rotate * dt * c_rotateSpeedMultiplier;
			if (m_state.m_cameraControlDirection > 180) m_state.m_cameraControlDirection -= 360;
			else if (m_state.m_cameraControlDirection < -180) m_state.m_cameraControlDirection += 360;

			var wasIgnoringHand = m_cameraControlStageIgnoreHand;
			camOffset = GetCameraOffsetFromFocus();

			// apply offset to dolly and rotate about cursor position
			var cameraTranslate = Vector3.zero;
			if (wasIgnoringHand == 0)
			{
				camXform.position = m_state.m_cameraControlFocus + camOffset;
				camXform.LookAt(m_state.m_cameraControlFocus, Vector3.up);
				var hitNew = Planecast(cam, screenReferencePoint, m_state.m_cameraControlFocus);
				cameraTranslate = hitOld - hitNew;
			}

			// now translate
			var fwdFlat = camXform.forward.GetXZNorm();
			var rightFlat = camXform.right.GetXZNorm();

			// keyboard translate
			cameraTranslate += rightFlat * movement.x + fwdFlat * movement.y;
			// mouse translate
			if (GetDragStarted())
			{
				m_mouseDragStartedLegally = mouseIsLegalForMove;
				m_lastMousePos = GetDragPosition();
				m_mouseDragPlaneOrigin = RaycastAtPoint(m_lastMousePos, out var hitDrag) ? hitDrag.point : Vector3.up * GlobalData.c_seaLevel;
				m_draggingMouse = Vector3.zero;
			}
			bool dragContinued = GetDragContinued(); 
			if ((dragContinued || (m_lastMousePos - m_draggingMouse).sqrMagnitude > .1f * .1f) && m_mouseDragStartedLegally && dragIsLegal)
			{
				if (dragContinued) m_draggingMouse = GetDragPosition();
				else if (movement.sqrMagnitude > .01f * .01f) m_lastMousePos = m_draggingMouse; // moved with keys, kill any residual drag movement
				var mouseAvg = Vector3.Lerp(m_lastMousePos, m_draggingMouse, .2f);
				var plane = new Plane(Vector3.up, m_mouseDragPlaneOrigin);
				var rayNew = m_camera.ScreenPointToRay(mouseAvg);
				if (plane.Raycast(rayNew, out var distance))
				{
					var newHit = rayNew.GetPoint(distance);
					cameraTranslate += m_mouseDragPlaneOrigin - newHit;
				}
				m_lastMousePos = mouseAvg;
			}
		
			// final position and clamping
			m_state.m_cameraControlFocus += cameraTranslate;
		}
		else
			camOffset = GetCameraOffsetFromFocus();

		ClampFocus(camXform.position.y);
		
		var groundY = Mathf.Max(GlobalData.c_seaLevel, m_state.m_cameraControlFocus.GroundPosition().y);
		m_state.m_cameraControlFocus.y = Mathf.Lerp(m_state.m_cameraControlFocus.y, groundY, .02f.TCLerp());
		
		camXform.position = m_state.m_cameraControlFocus + camOffset;
		camXform.LookAt(m_state.m_cameraControlFocus, Vector3.up);
		
		UpdateBlendCamera();
		
		ApplyTownCameraOverride();
		
		if (m_cameraControlStageIgnoreHand == 0)
			HandleCollision(camXform, oldPosition, zoom < 0);

		if (IsInPlayground && m_camera.transform.position.y<c_playgroundHeight + 10f)
			m_camera.transform.position = m_camera.transform.position.NewY(c_playgroundHeight + 10f);
		
		m_cameraFrustumPlanes = GeometryUtility.CalculateFrustumPlanes(Camera.main);
		
		return true;
	}
}
