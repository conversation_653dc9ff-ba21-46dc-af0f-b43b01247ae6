using System.Collections.Generic;
using UnityEngine;

public class ColourWithTimeOfDay : MonoBehaviour
{
    private Material[] m_materialsToChange;
    private Light[] m_lightsToChange;
    public bool m_affectMaterialTint = true;
    public bool m_affectMaterialEmission = true;
    public float m_materialSunIntensity = 1.0f / 50.0f;
    public float m_materialMoonIntensity = 0;
    public float m_lightSunIntensity = 1.0f / 100.0f;
    public float m_lightMoonIntensity = 1.0f / 200.0f;

    void Start()
    {
        var renderer = GetComponent<Renderer>();
        m_materialsToChange = renderer != null ? renderer.materials : null;
        m_lightsToChange = GetComponents<Light>();
    }

    void Update()
    {
        var sky = Marcos_Procedural_Sky.Me;
        var lightColour = sky.HDRP_Sun_Color_Mixed * (sky.HDRP_Sun_Intensity_Mixed * m_lightSunIntensity) + sky.HDRP_Moon_Color_Mixed * (sky.HDRP_Moon_Intensity_Mixed * m_lightMoonIntensity);
        var materialColour = sky.HDRP_Sun_Color_Mixed * (sky.HDRP_Sun_Intensity_Mixed * m_materialSunIntensity) + sky.HDRP_Moon_Color_Mixed * (sky.HDRP_Moon_Intensity_Mixed * m_materialMoonIntensity);
        materialColour.a = 1;
        if (m_materialsToChange != null)
        {
            foreach (var mat in m_materialsToChange)
            {
                if (m_affectMaterialTint)
                    Decoration.SetTintProperty(mat, 0, materialColour);
                if (m_affectMaterialEmission)
                    mat.SetColor("_EmisivColor", materialColour);
            }
        }
        if (m_lightsToChange != null)
        {
            foreach (var light in m_lightsToChange)
            {
                light.color = lightColour;
            }
        }
    }
}
