using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;

public class LoadDayController : MonoBehaviour
{
    public GameObject m_debugButton;
    private bool m_isDebugMode = false;
    private bool m_isClosing = false;
    
    void OnEnable()
    {
        m_isClosing = false;
        RefreshEntries();
#if UNITY_EDITOR || DEVELOPMENT_BUILD
#else
        m_debugButton.SetActive(false);
#endif
    }

    void RefreshEntries()
    {
        var scroller = GetComponentInChildren<ScrollRect>();
        var content = scroller.content;
        content.DestroyChildren(true, 1);
        var entryTemplate = content.GetChild(0);
        entryTemplate.gameObject.SetActive(false);
        var files = GetFileList();
        for (int i = 0; i < files.Count; ++i)
        {
            var (day, label, file) = files[i];
            var entry = Instantiate(entryTemplate, content);
            entry.gameObject.SetActive(true);
            var text = entry.GetComponentInChildren<TMPro.TextMeshProUGUI>();
            text.text = label;
            var button = entry.GetComponent<Button>();
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() =>
            {
                if (m_isClosing) return;
                Utility.ShowDialog($"Load Day {day}", "Are you sure you want to load this day?\nYour current save will be overwritten", false, "Yes", "No", _button =>
                {
                    if (_button == 0)
                    {
                        m_isClosing = true;
                        this.DoAfter(1, () => GameManager.Me.Load(file, m_isDebugMode));
                    }
                });
            });
        }
    }

    List<(int, string, string)> GetFileList()
    {
        var res = new List<(int, string, string)>();
        if (m_isDebugMode)
        {
            var resources = Resources.LoadAll<TextAsset>("Days");
            foreach (var resource in resources)
            {
                var state = GameManager.Me.LoadFromJson(resource.text, resource.name);
                var info = GameManager.Me.GetCalendarInfo(state);
                var display = GameManager.Me.GetCalendarInfoString(info);
                if (string.IsNullOrEmpty(display) == false) res.Add((info.m_dayNumber, display, resource.text));
            }
        }
        else
        {
            var dir = new DirectoryInfo(Application.persistentDataPath);
            var files = dir.GetFiles("Day*.dat", SearchOption.TopDirectoryOnly);
            foreach (var file in files)
            {
                var state = GameManager.Me.LoadFromFile(file.FullName);
                var info = GameManager.Me.GetCalendarInfo(state);
                var display = GameManager.Me.GetCalendarInfoString(info);
                display = $"{display} - ({file.LastWriteTime.ToShortDateString()} {file.LastWriteTime.ToShortTimeString()})";
                if (string.IsNullOrEmpty(display) == false) res.Add((info.m_dayNumber, display, file.FullName));
            }
        }
        res.Sort((a, b) => a.Item1.CompareTo(b.Item1));
        return res;
    }

    public void ToggleDebug()
    {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        m_isDebugMode = !m_isDebugMode;
        RefreshEntries();
#endif
    }
}
