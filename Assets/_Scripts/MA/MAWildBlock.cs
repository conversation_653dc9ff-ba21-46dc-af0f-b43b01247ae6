using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

[RequireComponent(typeof(Block))]
public class MAWildBlock : NG<PERSON>egacyBase, IPointerDownHandler, IStrikePoint
{
    [NonSerialized]
    public GameState_WildBlock m_wildBlockState = null;
    #if UNITY_EDITOR
	public string m_debugBlockID;
	#endif
    
    public bool m_componentsChanged = false; 
    private Rigidbody m_rigidbody = null;
    private Block m_block = null;
    private PathBlock m_pathBlock = null;
    private bool m_shouldBlockPathSmoothed = false;
    private int m_shouldBlockPathCounter = 0;
    private PlantController m_plantController;
    private Vector3 m_previousPosition;
    
    private AlwaysFaceCamera[] m_alwaysFaceCameraComponents;
		private BoxCollider m_bc;
    
    Vector3 IStrikePoint .StrikePoint => transform.position + Vector3.up * 2;
    
    private void Awake()
    {
	    m_rigidbody = GetComponent<Rigidbody>();
        if (m_rigidbody == null) m_rigidbody = gameObject.AddComponent<Rigidbody>();
	    m_rigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
        m_block = GetComponent<Block>();
        // Collider col = GetComponentInChildren<Collider>();
        // col.isTrigger = false;
        // //TS - removing the above lines. e.g this would cause Door-triggers to be set as non-trigger. after adding the block to a building the door would prevent entry/exit
		
        m_alwaysFaceCameraComponents = GetComponentsInChildren<AlwaysFaceCamera>();
        ToggleAlwaysFaceCamera(false);
		ToggleDayNightControls(false);

        PhysicsAudio.Create(gameObject, "PlaySound_BalloonDropCollision", "ThrowableObjectImpactSpeed");
        
        InitialiseBouyancy();
    }
    
    private void ToggleAlwaysFaceCamera(bool _enable)
	{
	    foreach (var afc in m_alwaysFaceCameraComponents)
		    afc.enabled = _enable;
	}

	private void ToggleDayNightControls(bool _enable)
	{
		foreach (var dnc in GetComponentsInChildren<DayNightControl>())
			dnc.SetHardDisabled(!_enable);
	}

	public void UpdateComponentIds(bool _resetIds = false)
	{
		m_wildBlockState.m_componentIds.Clear();
		var block = GetComponent<Block>();
		Block.GenerateComponentInfo(new List<Block>() { block }, m_wildBlockState.m_componentIds, _resetIds);
	}

	private void PostLoad()
    {
	    m_previousPosition = transform.position;
    }

    void OnDestroy()
    {
	    ToggleAlwaysFaceCamera(true);
	    ToggleDayNightControls(true);
        if (m_rigidbody != null)
            Destroy(m_rigidbody);
        if (m_pathBlock != null)
			Destroy(m_pathBlock);
        PhysicsAudio.Destroy(gameObject);
    }

    void CheckPathBlock()
    {
	    // m_rigidbody.IsSleeping() happens far too late
	    // however, using velocity directly is too noisy so wait until it's been in the same state for a few frames before changing
	    bool shouldBlockPath = m_rigidbody.linearVelocity.sqrMagnitude < .1f * .1f;
	    if (shouldBlockPath != m_shouldBlockPathSmoothed)
	    {
		    m_shouldBlockPathSmoothed = shouldBlockPath;
		    m_shouldBlockPathCounter = 0;
	    }
	    ++m_shouldBlockPathCounter;
	    if (m_shouldBlockPathCounter > 5)
	    {
		    if (m_pathBlock == null && shouldBlockPath)
		    {
					// RW-24-APR-25: Create the path block for Wild Blocks using a box collider
					// for greater accuracy. Because TotalVisualBounds is in world space, we need to take the 
					// transform off then put it back on afterwards.
					var cachedPosition = transform.position;
					var cachedRotation = transform.rotation;
					transform.position = Vector3.zero;
					transform.rotation = Quaternion.identity;
					var bounds = ManagedBlock.GetTotalVisualBounds(gameObject);
					transform.position = cachedPosition;
					transform.rotation = cachedRotation;

					var newgo = new GameObject("PathBlockCollider");
					newgo.transform.SetParent(transform, false);
					m_bc = newgo.AddComponent<BoxCollider>();
					m_bc.size = bounds.size;
					m_bc.center = bounds.center;
					m_bc.enabled = false;
			    m_pathBlock = PathBlock.Create(gameObject, m_bc, 0.2f, true);
		    }
		    else if (m_pathBlock != null && !shouldBlockPath)
		    {
					Destroy(m_bc.gameObject);
			    Destroy(m_pathBlock);
			    m_pathBlock = null;
		    }
	    }
    }
    
    bool m_haveShownRigidbodyWarning = false;

    private bool CheckRigidbody()
    {
	    if (m_rigidbody == null)
	    {
		    if (m_haveShownRigidbodyWarning == false)
		    {
			    Debug.LogError($"CheckRigidbody failed for {name}");
			    m_haveShownRigidbodyWarning = true;
		    }
			return false;
	    }
	    return true;
    }
    
    public bool IsDisabledByPlants => m_wildBlockState.m_plantLevel > 0;

	private void UpdatePlantState()
	{
		const float c_movementThresholdToDestroyAllPlants = 1f;
		const float c_plantLevelIncreasePerSecond = 0;//.01f;
		
		/*var pos = transform.position;
		var movement = pos - m_previousPosition;
		if (movement.sqrMagnitude > c_movementThresholdToDestroyAllPlants * c_movementThresholdToDestroyAllPlants)
		{
			m_previousPosition = pos;
			m_wildBlockState.m_plantLevel = 0;
		}*/
		m_wildBlockState.m_plantLevel = Mathf.Min(1, m_wildBlockState.m_plantLevel + Time.deltaTime * c_plantLevelIncreasePerSecond);
		m_plantController.SetLevel(Mathf.Lerp(m_plantController.m_plantLevel, m_wildBlockState.m_plantLevel, .1f), m_wildBlockState.m_plantLevel);
	}

    private void Update()
    {
#if UNITY_EDITOR
	    var index = GameManager.Me.m_state.m_wildBlocks.IndexOf(m_wildBlockState);
	    m_debugBlockID = $"{index} ({m_wildBlockState.m_id})";
#endif
	    UpdatePlantState();
	    
	    if (CheckRigidbody() == false) return;
	    CheckPathBlock();

	    Transform tr = transform;
	    if (m_rigidbody.IsSleeping() == false)
	    {
		    if (m_wildBlockState != null)
		    {
			    m_wildBlockState.m_angularVelocity = m_rigidbody.angularVelocity;
			    m_wildBlockState.m_velocity = m_rigidbody.linearVelocity;
			    m_wildBlockState.m_position = m_rigidbody.position;
			    m_wildBlockState.m_rotation = m_rigidbody.rotation.eulerAngles;
		    }
		    else
		    {
			    Debug.LogError($"{GetType().Name} - TrySetBlockPhysicsToStatic - wild block not found in state: {m_block.BlockID}");
		    }

		    return;
	    }
	    
	    m_wildBlockState.m_angularVelocity = Vector3.zero;
	    m_wildBlockState.m_velocity = Vector3.zero;
	    m_wildBlockState.m_position = tr.position;
	    m_wildBlockState.m_rotation = tr.eulerAngles;
    }

    Vector3[] m_objectSpaceCorners = new Vector3[8];
	private void InitialiseBouyancy()
    {
	    var (pos, rot, scale) = (transform.position, transform.rotation, transform.localScale);
	    transform.position = Vector3.zero;
	    transform.rotation = Quaternion.identity;
	    var parentScale = transform.parent?.lossyScale ?? Vector3.one;
	    transform.localScale = new Vector3(1.0f / parentScale.x, 1.0f / parentScale.y, 1.0f / parentScale.z);
	    var bounds = ManagedBlock.GetTotalVisualBounds(gameObject);
	    (transform.position, transform.rotation, transform.localScale) = (pos, rot, scale);
	    for (int i = 0; i < 8; ++i)
	    {
		    float x = Mathf.Lerp(bounds.min.x, bounds.max.x, (i >> 0) & 1);
		    float y = Mathf.Lerp(bounds.min.y, bounds.max.y, (i >> 1) & 1);
		    float z = Mathf.Lerp(bounds.min.z, bounds.max.z, (i >> 2) & 1);
		    m_objectSpaceCorners[i] = new Vector3(x, y, z);
	    }
    }

    static float s_bouyancyForcePerMeter = 1.5f;
    static float s_bouyancyOffset = 2.5f;
    static float s_bouyancyMaxDepth = 4;
    private void RunBouyancy()
	{
		int submergedCorners = 0;
		for (int i = 0; i < 8; ++i)
		{
			var point = transform.TransformPoint(m_objectSpaceCorners[i]);
			var underSea = GlobalData.c_seaLevel - point.y + s_bouyancyOffset;
			if (underSea <= 0) continue;
			underSea = Mathf.Min(s_bouyancyMaxDepth, underSea);
			var bouyancy = underSea * s_bouyancyForcePerMeter;
			m_rigidbody.AddForceAtPosition(Vector3.up * bouyancy, point, ForceMode.Acceleration);
			++submergedCorners;
		}
		if (submergedCorners > 0)
		{
			m_rigidbody.linearVelocity = Vector3.Scale(m_rigidbody.linearVelocity, new Vector3(.95f, .95f, .95f));
			m_rigidbody.angularVelocity = Vector3.Scale(m_rigidbody.angularVelocity, new Vector3(.99f, .99f, .99f));

			const float c_tidalStrength = 1.5f;
			var containingForce = Vector3.zero;
			if (c_tidalStrength > 0)
			{
				var fromMid = transform.position - (GlobalData.c_terrainMin + GlobalData.c_terrainMax) * .5f;
				containingForce.x = Mathf.Sign(fromMid.x) * -c_tidalStrength;
				containingForce.z = Mathf.Sign(fromMid.z) * -c_tidalStrength;
			}
			else
			{
				var fromMin = transform.position - GlobalData.c_terrainMin;
				var fromMax = transform.position - GlobalData.c_terrainMax;
				if (fromMin.x < 0) containingForce += Vector3.right * -fromMin.x;
				else if (fromMax.x > 0) containingForce += Vector3.right * -fromMax.x;
				if (fromMin.z < 0) containingForce += Vector3.forward * -fromMin.z;
				else if (fromMax.z > 0) containingForce += Vector3.forward * -fromMax.z;
			}
			if (containingForce.sqrMagnitude > 0) m_rigidbody.AddForce(containingForce, ForceMode.Acceleration);
		}
	}
    
    void FixedUpdate() => RunBouyancy();

    public static MAWildBlock Load(GameState_WildBlock w, GameObject _o)
    {
		Rigidbody rb = _o.GetOrAddComponent<Rigidbody>();
		rb.isKinematic = false;
		rb.mass = 1000;
		rb.linearVelocity = w.m_velocity;
		rb.angularVelocity = w.m_angularVelocity;
		rb.transform.position = w.m_position;
		rb.transform.rotation = Quaternion.Euler(w.m_rotation);
		MAWildBlock maWildBlock = _o.AddComponent<MAWildBlock>();
		maWildBlock.m_wildBlockState = w;
		w.WildBlock = maWildBlock;

		if (w.m_hasEverBeenUnlocked == false)
			if (DistrictManager.Me.IsWithinDistrictBounds(w.m_position, true))
				w.m_hasEverBeenUnlocked = true;
		var block = _o.GetComponent<Block>(); 
		block.m_lastWildBlockID = w.m_id;
		block.m_visualSwitcher?.SwitchTo(0);
		block.m_visualSwitcher?.Reset();

		DesignTableManager.SetWorldTriPlanar(block.gameObject, false);
		
		if (w.m_hasEverBeenUnlocked)
			_o.IgnoreDistrictFilter(true);
			//_o.GetOrAddComponent<DistrictOverride>();

		maWildBlock.m_plantController = _o.AddComponent<PlantController>();
		if (_o.GetComponentInChildren<DTDragBlock>() == null)
			DesignTableManager.CreateAllDrags(_o);

		/*BoxCollider b = rb.transform.GetComponent<BoxCollider>();
		Vector3[] boundingVertices = new Vector3[8];
		boundingVertices[0] = rb.transform.TransformPoint(b.center + new Vector3(-b.size.x, -b.size.y, -b.size.z)*0.5f);
		boundingVertices[1] = rb.transform.TransformPoint(b.center + new Vector3(b.size.x, -b.size.y, -b.size.z)*0.5f);
		boundingVertices[2] = rb.transform.TransformPoint(  b.center + new Vector3(b.size.x, -b.size.y, b.size.z)*0.5f);
		boundingVertices[3] = rb.transform.TransformPoint(b.center + new Vector3(-b.size.x, -b.size.y, b.size.z)*0.5f);
		boundingVertices[4] = rb.transform.TransformPoint(b.center + new Vector3(-b.size.x, b.size.y, -b.size.z)*0.5f);
		boundingVertices[5] = rb.transform.TransformPoint(b.center + new Vector3(b.size.x, b.size.y, -b.size.z)*0.5f);
		boundingVertices[6] = rb.transform.TransformPoint(b.center + new Vector3(b.size.x, b.size.y, b.size.z)*0.5f);
		boundingVertices[7] = rb.transform.TransformPoint(b.center + new Vector3(-b.size.x, b.size.y, b.size.z)*0.5f);

		float y = 0;
		float diff = 0;
		foreach(Vector3 vert in boundingVertices)
		{
			float realY = GlobalData.Me.GetRealHeight(vert);
			if(realY > vert.y)
			{
				if(y < realY)
				{
					y = realY;
					diff = realY - y;
				}
			}
		}

		if(y != 0)
		{
			Transform tr = rb.transform;
			Vector3 newPos = tr.position;
			newPos.y += diff;
			tr.position = newPos;//TODO: TS - why is it lower sometimes..
		}*/

		maWildBlock.PostLoad();
		
		return maWildBlock;
    }

    private static bool ApproximatelyV3(Vector3 v1, Vector3 v2, float epsilon = 0.1f)
    {
	    bool CustomApprox(float a, float b, float _epsilon = 0.1f)
	    {
		    float absA = Mathf.Abs(a);
		    float absB = Mathf.Abs(b);
		    float max = Mathf.Max(absA, absB);
		    float min = Mathf.Min(absA, absB);
		    return (max - min) <= _epsilon;
	    }
		
	    return CustomApprox(v1.x, v2.x, epsilon) &&
	           CustomApprox(v1.y, v2.y, epsilon) &&
	           CustomApprox(v1.z, v2.z, epsilon);
    }

    public void OnPointerDown(PointerEventData eventData)
    {
		if (m_wildBlockState == null) return;
		if (m_wildBlockState.m_hasEverBeenUnlocked == false && DistrictManager.Me.IsWithinDistrictBounds(transform.position, true) == false) return;
		if(GameManager.Me.CameraExceededMaxInteractionDistance()) return;
		if (m_wildBlockState.m_plantLevel > 0)
		{
			const float c_plantLevelRemovedPerTap = .2f;
			m_wildBlockState.m_plantLevel = Mathf.Max(0, m_wildBlockState.m_plantLevel - c_plantLevelRemovedPerTap);
			PlantController.TriggerDestroyFX(Utility.mousePosition, m_wildBlockState.m_plantLevel <= 0);
			if (m_wildBlockState.m_plantLevel <= 0)
			{
				var message = m_block.BlockInfo.m_displayName;
				//foreach (var bc in m_block.GetComponentsInChildren<BCBase>())
				//	if (string.IsNullOrEmpty(bc.Title) == false)
				//		message = bc.Title;
				m_plantController.TemporaryHold = true;
				Utility.ShowObjectMessage(message, Vector3.up * .1f, gameObject, 5, () => m_plantController.TemporaryHold = false, true);
			}
		}
    }
}
