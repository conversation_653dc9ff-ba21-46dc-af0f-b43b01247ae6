using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.PlayerLoop;

public class BCAutomator : BCBase
{
    [Save] public float m_producesPerSecond = 0.1f;

    virtual public void UpdateInternal()
    {
        base.UpdateInternal();
        if (m_building.IsPaused) return;
        /*var work = m_producesPerSecond * Time.deltaTime;
        if(m_building.UpdateWork(work))
            AnimateBlock(1);*/
    }
    
}
