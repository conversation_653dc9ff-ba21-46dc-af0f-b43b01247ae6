using UnityEngine;

public class MAArmour : MonoBehaviour
{
    public const float c_DamagePerArmourPoint = 10f;

    [SerializeField]
    private float m_maxArmourPoints;
    public float MaxArmourPoints
    {
        get { return m_maxArmourPoints; }
        set { m_maxArmourPoints = value; m_armourPoints = value; }
    }

    [SerializeField]
    private float m_armourPoints;
	public float ArmourPoints
	{
		get { return m_armourPoints; }
		set { m_armourPoints = Mathf.Min(m_maxArmourPoints , m_armourPoints + value); }
	}

	public float DefenseValue => m_armourPoints * c_DamagePerArmourPoint;

	public float DamageTaken(float damage)
    {
        float rem = damage - DefenseValue;
        if (rem > 0)
        {
            m_armourPoints = 0;
            return rem;
        }
        m_armourPoints = -rem / c_DamagePerArmourPoint;
        return 0;
    }
}
