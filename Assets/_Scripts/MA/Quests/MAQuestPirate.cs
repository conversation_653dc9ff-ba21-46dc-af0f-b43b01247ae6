using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Playables;

public class MAQuestPirate : MAQuestBase
{
    public enum QuestType
    {
        Wood = 0,
        Metal = 1,
        Cloth = 2,
        Count
    }

    public enum AnimState
    {
        PreWood = 0,
        PostWood = 1,
        PostMetal = 2,
        PostCloth = 3
    }

    public AkSwitchHolder[] m_songMusic = new AkSwitchHolder[(int)QuestType.Count + 1] { new(), new(), new(), new() };
    public Animator m_pirateShip, m_pirateShipMovement;
    public MAQuestCutscene m_sailSequence1, m_sailSequence2, m_buildSequence1, m_buildSequence2, m_buildSequence3;
    public Transform m_captainHolder, m_firstMateHolder, m_secondMateHolder, m_stockpileHolder, m_shipLookAtTransform;

    // TODO: make settings public
    private float m_pirateTurnSpeed = 180.0f;
    private float m_depleteStockpileTime = 5.0f;
    private float[] m_buildTimes = new float[(int)QuestType.Count] { 13.0f, 12.0f, 8.0f };

    private MAFlowCharacter m_pirateCaptain, m_pirateFirstMate, m_pirateSecondMate;
    private BCStockIn m_stockpile;
    Coroutine m_playSongCoroutine, m_buildShipCoroutine, m_launchShipCoroutine, m_playBuildSequenceCoroutine;
    private int m_buildID;

    // saved data
    private QuestType m_questType = QuestType.Count;
    private bool m_isStockpileEnabled = false;
    private AnimState m_animState = AnimState.PreWood;

    protected override void Awake()
    {
        base.Awake();
        m_buildID = Animator.StringToHash("Build");
    }

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_status == QuestStatus.InProgress)
        {
        }
    }

    private void InitReferences()
    {
        if (m_pirateCaptain == null)
        {
            m_pirateCaptain = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateCaptain") as MAFlowCharacter;

            if (m_pirateCaptain != null)
            {
                m_lipSyncers["PirateCaptain"] = m_pirateCaptain;

                var ls = m_pirateCaptain.SetLipSync(false);
                ls.m_deltaRot = -18.0f;
            }
        }

        if (m_pirateFirstMate == null)
        {
            m_pirateFirstMate = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateFirstMate") as MAFlowCharacter;
        }

        if (m_pirateSecondMate == null)
        {
            m_pirateSecondMate = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateSecondMate") as MAFlowCharacter;
        }

        if(m_stockpile == null)
        {
            MABuilding building = MABuilding.FindBuilding("QuestPirateStockpile", true);

            if(building != null)
            {
                var stocks = building.BuildingComponents<BCStockIn>();

                foreach(var stock in stocks)
                {
                    if(stock != null)
                    {
                        m_stockpile = stock;
                        break;
                    }
                }
            }

            SetStockpileEnabled(m_isStockpileEnabled);
        }

        ShowStockpile(m_pirateCaptain != null); //KW: hide the stockpile until the pirates are spawned

        if (m_animState >= AnimState.PostMetal)
        {
            SetPiratesOnShip(); //KW: this sets the stockpile on the ship and hides it so don't move this above ShowStockpile
        }
    }
    

    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("PlaySongComplete"))
        {
            return m_playSongCoroutine == null ? 0.0f : 1.0f;
        }

        if (_objective.Contains("BuildShipComplete"))
        {
            return m_buildShipCoroutine == null ? 0.0f : 1.0f;
        }

        if (_objective.Contains("LaunchShipComplete"))
        {
            return m_launchShipCoroutine == null ? 0.0f : 1.0f;
        }

        return 1.0f;
    }

    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("SetQuestType"))
        {
            QuestType questType = QuestType.Count;

            if(Enum.TryParse(split[1], out questType))
            {
                SetQuestType(questType);
            }
        }

        if (split[0].Contains("PlaySong"))
        {
            PlaySong();
        }

        if (split[0].Contains("EnableStockpile"))
        {
            bool enabled = false;

            if (bool.TryParse(split[1], out enabled))
            {
                SetStockpileEnabled(enabled);
            }
        }

        if (split[0].Contains("BuildShip"))
        {
            BuildShip();
        }

        if (split[0].Contains("LaunchShip"))
        {
            LaunchShip();
        }
    }

    private void SetQuestType(QuestType _questType)
    {
        m_questType = _questType;
    }

    private void PlaySong()
    {
        if (m_playSongCoroutine == null)
        {
            m_playSongCoroutine = StartCoroutine(Co_PlaySong());
        }
    }

    private IEnumerator Co_PlaySong()
    {
        PlaySongMusic();

        yield return new WaitForSeconds(3.0f);

        m_playSongCoroutine = null;

        yield return null;
    }

    private void PlaySongMusic()
    {
        if (m_questType < QuestType.Count)
        {
            // m_songMusic[(int)m_questType].Play(gameObject, AkEventHolder.EBus.Music);
        }
    }

    private void SetStockpileEnabled(bool _enabled)
    {
        if (m_stockpile != null)
        {
            m_stockpile.GetStock().RemoveEmptyStockAndClearNeededToProduce();

            if (_enabled && m_questType < QuestType.Count)
            {
                m_stockpile.GetStock().AddOrCreateStock(GetResource(), 0, 1f);
            }
        }

        m_isStockpileEnabled = _enabled;
    }

    private void BuildShip()
    {
        if (m_buildShipCoroutine == null)
        {
            m_buildShipCoroutine = StartCoroutine(Co_BuildShip());
        }
    }

    private IEnumerator Co_BuildShip()
    {
        // PiratesLookAt(m_shipLookAtTransform.position);
        PlayShipAnimation();
        PlayBuildSequence();

        int stockCount = m_stockpile.GetStock().GetStock(GetResource());    

        if (stockCount <= 0)
        {
            yield return new WaitForSeconds(m_depleteStockpileTime);
        }
        else
        {
            float depleteStockpileDelay = m_depleteStockpileTime / stockCount;

            while (DepleteStockpile())
            {
                yield return new WaitForSeconds(depleteStockpileDelay);
            }
        }

        float buildTime = m_buildTimes[(int)m_questType] - m_depleteStockpileTime;

        yield return new WaitForSeconds(buildTime);

        // Vector3 defaultPos = new Vector3(-560.57f, 91.58f, -93.68f);
        // PiratesLookAt(defaultPos);

        m_buildShipCoroutine = null;

        yield return null;
    }

    private void LaunchShip()
    {
        if (m_launchShipCoroutine == null)
        {
            m_launchShipCoroutine = StartCoroutine(Co_LaunchShip());
        }
    }

    private IEnumerator Co_LaunchShip()
    {
        MAQuestCutscene sailSequence = m_questType == QuestType.Metal ? m_sailSequence1 : m_sailSequence2;

        yield return sailSequence.Co_Play();

        m_launchShipCoroutine = null;

        yield return null;
    }

    private void PlayBuildSequence()
    {
        if (m_playBuildSequenceCoroutine == null)
        {
            m_playBuildSequenceCoroutine = StartCoroutine(Co_PlayBuildSequence());
        }
    }

    private IEnumerator Co_PlayBuildSequence()
    {
        MAQuestCutscene buildSequence = m_questType == QuestType.Wood ? m_buildSequence1 : m_questType == QuestType.Metal ? m_buildSequence2 : m_buildSequence3;

        yield return buildSequence.Co_Play();

        m_playBuildSequenceCoroutine = null;

        yield return null;
    }

    public void SetPiratesOnShip()
    {
        SetPirateOnShip(m_pirateCaptain, m_captainHolder);
        SetPirateOnShip(m_pirateFirstMate, m_firstMateHolder);
        SetPirateOnShip(m_pirateSecondMate, m_secondMateHolder);
        SetStockpileOnShip();
    }

    private void SetPirateOnShip(MAFlowCharacter _pirate, Transform _holder)
    {
        if (_pirate != null && _holder != null)
        {
            _pirate.transform.SetParent(_holder);
            _pirate.transform.ResetLocal();
            _pirate.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        }
    }

    private void SetStockpileOnShip()
    {
        if(m_stockpile != null && m_stockpileHolder != null)
        {
            Transform stockpileTransform = m_stockpile.Building.transform;
            stockpileTransform.SetParent(m_stockpileHolder);
            stockpileTransform.ResetLocal();
            ShowStockpile(false);
        }
    }

    private void ShowStockpile(bool _show)
    {
        if (m_stockpile != null)
        {
            Transform renderers = m_stockpile.Building.transform.FindChildRecursiveByName("Renderers");

            if (renderers != null)
            {
                renderers.gameObject.SetActive(_show);
            }
        }
    }

    private void PlayShipAnimation()
    {
        if (m_pirateShip != null)
        {
            m_pirateShip.SetTrigger(m_buildID);

            switch (m_questType)
            {
                case QuestType.Wood:
                    m_animState = AnimState.PostWood;
                    break;
                case QuestType.Metal:
                    m_animState = AnimState.PostMetal;
                    break;
                case QuestType.Cloth:
                    m_animState = AnimState.PostCloth;
                    break;
                default:
                    break;
            }

            m_pirateShip.SetInteger(m_stateID, (int)m_animState);
        }
    }

    public void PlayShipMovementAnimation()
    {
        if (m_pirateShipMovement != null)
        {
            switch (m_questType)
            {
                case QuestType.Metal:
                case QuestType.Cloth:
                    m_pirateShipMovement.SetTrigger(m_buildID);
                    m_pirateShipMovement.SetInteger(m_stateID, (int)m_animState);
                    break;
                default:
                    break;
            }    
        }
    }

    private bool DepleteStockpile()
    {
        if (m_stockpile != null && m_questType < QuestType.Count)
        {
            return m_stockpile.ConsumeStock(GetResource());
        }

        return false;
    }

    private NGCarriableResource GetResource()
    {
        switch (m_questType)
        {
            case QuestType.Wood:
                return NGCarriableResource.GetInfo("Timber");
            case QuestType.Metal:
                return NGCarriableResource.GetInfo("Metal");
            case QuestType.Cloth:
                return NGCarriableResource.GetInfo("Fabric");
            default:
                return null;
        }
    }

    private void PiratesLookAt(Vector3 _target)
    {
        m_pirateCaptain.LookAt(_target, m_pirateTurnSpeed);
        m_pirateFirstMate.LookAt(_target, m_pirateTurnSpeed);
        m_pirateSecondMate.LookAt(_target, m_pirateTurnSpeed);
    }

    public class SaveLoadQuestPirateContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestPirateContainer() : base() { }
        public SaveLoadQuestPirateContainer(MAQuestBase _base) : base(_base) { }
        [Save] public QuestType m_questType;
        [Save] public int m_isStockpileEnabled;
        [Save] public AnimState m_animState;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestPirateContainer(this);

        saveContainer.m_questType = m_questType;
        saveContainer.m_isStockpileEnabled = m_isStockpileEnabled ? 1 : 0;
        saveContainer.m_animState = m_animState;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestPirateContainer;
        if (saveContainer != null)
        {
            SetQuestType(saveContainer.m_questType);
            m_isStockpileEnabled = saveContainer.m_isStockpileEnabled > 0;
            m_animState = saveContainer.m_animState;

            if(m_pirateShip != null)
            {
                m_pirateShip.SetInteger(m_stateID, (int)m_animState);
            }

            if (m_pirateShipMovement != null)
            {
                m_pirateShipMovement.SetInteger(m_stateID, (int)m_animState);
            }
        }
    }
}
