
using UnityEngine;

public class MAQuestCaveBlockerMetal : MAQuestBase
{
    public int m_swordRackUID;
    private MABuilding m_swordRack;
    private MAFlowCharacter m_questCharacter;

    protected override void OnPostLoad()
    {
    }

    private void SetSwordRack()
    {
        m_swordRack = MABuilding.FindBuilding(m_swordRackUID.ToString());
        if (m_swordRack == null)
        {
            Debug.Log("MAQuestCaveBlockerMetal - Could not find swordRack");
        }
    }

    private void SetQuestCharacter()
    {
        m_questCharacter = MAFlowCharacter.FindCharacter(m_questWorkerInfoName);
        if (m_questCharacter == null)
        {
            Debug.Log("MAQuestCaveBlockerMetal - Could not find quest<PERSON>haracter");
        }
    }

    // private void SpawnSwordRack()
    // {
    //     var prefab = Resources.Load($"_Prefabs/Quests/QuestSwordRack"); 
    //     GameObject ob = Object.Instantiate(prefab) as GameObject;
    //     m_swordRack = ob.GetComponent<MABuilding>();
    // }

    private void AttachSwordToQuestGiver()
    {
        SetSwordRack();
        SetQuestCharacter();
            
        if (m_swordRack != null && m_questCharacter != null)
        {
            BCQuestStockIn stockIn = m_swordRack.GetComponentInChildren<BCQuestStockIn>();
            NGCarriableResource weapon = stockIn.GetResource();
            if (weapon != null)
            {
                m_questCharacter.SetWeaponDesign(weapon.GetProduct().m_design);
                stockIn.DestroyStock();
            }
        }
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("AttachSwordToQuestGiver"))
        {
            AttachSwordToQuestGiver();
        }
    }
}
