using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Unity.EditorCoroutines.Editor;
using UnityEditor;
using UnityEngine;
using static KitBashOptimiseManager;


public class KitBashOptimiseWindow : EditorWindow
{
    public const string c_ConfigFolder = "Assets/Resources/_Prefabs/_Blocks";
    public const string c_DefaultInputFolder = "Assets/_Prefabs/_Blocks";
    public const string c_DefaultOutputFolder = "Assets/Resources/_Prefabs/_Blocks";

    [Serializable]
    public class Configuration
    {
        public string name = "New Configuration";

        public string directoryFrom;
        public string directoryTo;
        public bool superFolder;
        public string atlasName = "Atlas";
        public bool enabled = true;

        public Configuration(string _folderIn, string _folderOut, bool _superFolder)
        {
            name = Path.GetFileName(_folderIn);
            directoryFrom = $"Assets/{Path.GetRelativePath(Application.dataPath, _folderIn).Replace('\\', '/')}";
            directoryTo = $"Assets/{Path.GetRelativePath(Application.dataPath, _folderOut).Replace('\\', '/')}";

            superFolder = _superFolder;
        }
    }

    [Serializable]
    private class ConfigurationListWrapper
    {
        public List<Configuration> configurations = new();
    }

    private bool forceUpdate;
    private List<Configuration> configurations = new();
    private int selectedConfigIndex;

    private Vector2 scrollPosition;
    private bool configSectionExpanded;
    private bool awake;
    private bool isRunning;
    private EditorCoroutine currentOperation;

    [MenuItem("Art Tools/Optimise Kits")]
    public static void GetWindow()
    {
        GetWindow<KitBashOptimiseWindow>("Optimise Kits");
    }

    void WakeMeUp()
    {
        awake = true;

        var path = $"{c_ConfigFolder}/kitbash_default.json";
        if (File.Exists(path))
        {
            string json = File.ReadAllText(path);
            var wrapper = JsonUtility.FromJson<ConfigurationListWrapper>(json);
            configurations = wrapper.configurations;

            // Ensure all loaded configurations are enabled by default if the JSON doesn't contain enabled field
            foreach (var config in configurations)
            {
                // If the JSON doesn't contain the enabled field for this config, default to true
                if (!json.Contains($"\"enabled\""))
                    config.enabled = true;
            }
        }
    }

    void OnGUI()
    {
        if (!awake)
        {
            WakeMeUp();
        }

        float originalLabelWidth = EditorGUIUtility.labelWidth;
        try
        {
            EditorGUIUtility.labelWidth = 180;
            DrawWindowHeader();
            EditorGUILayout.Space(10);
            DrawMain();
        }
        finally
        {
            EditorGUIUtility.labelWidth = originalLabelWidth;
        }
    }

    private void DrawWindowHeader()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.BeginHorizontal();
        {
            EditorGUILayout.LabelField("Kit Bash Optimiser", GetHeaderStyle());

            if (isRunning)
            {
                GUI.backgroundColor = Color.red;
                if (GUILayout.Button("Force Stop", GUILayout.Width(80)))
                {
                    ForceStop();
                }

                GUI.backgroundColor = Color.white;
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);
        DrawHorizontalLine();
        EditorGUILayout.Space(15);
    }

    private void ForceStop()
    {
        if (currentOperation != null)
        {
            EditorCoroutineUtility.StopCoroutine(currentOperation);
            currentOperation = null;
        }

        isRunning = false;
        KillAllOperations();
    }

    private void DrawMain()
    {
        DrawRunAllButton();
        EditorGUILayout.Space(10);

        scrollPosition =
            EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));

        DrawToggleField(ref forceUpdate, "Force Update:");
        EditorGUILayout.Space(10);

        DrawConfigurationSelector();
        EditorGUILayout.Space(15);

        if (GUILayout.Button("Add Folder as Multiple Runs (Subfolders)", GUILayout.Height(30)))
        {
            AddFolderConfiguration(true);
        }

        EditorGUILayout.Space(10);

        if (GUILayout.Button("Add Folder as Single Run", GUILayout.Height(30)))
        {
            AddFolderConfiguration(false);
        }

        EditorGUILayout.Space(15);

        if (configurations.Count > 0)
        {
            Configuration config = configurations[selectedConfigIndex];

            DrawConfigurationSection(config);
            EditorGUILayout.Space(15);
        }

        DrawActionButtons();

        EditorGUILayout.EndScrollView();
    }

    private void AddFolderConfiguration(bool _superFolder)
    {
        string inputFolder = EditorUtility.OpenFolderPanel("Select Input Folder", c_DefaultInputFolder, "");
        if (string.IsNullOrEmpty(inputFolder))
            return;

        string outputFolder = EditorUtility.OpenFolderPanel("Select Output Folder", c_DefaultOutputFolder, "");
        if (string.IsNullOrEmpty(outputFolder))
            return;

        selectedConfigIndex = configurations.Count;
        configurations.Add(new Configuration(inputFolder, outputFolder, _superFolder));
    }

    private void DrawConfigurationSelector()
    {
        EditorGUILayout.BeginHorizontal();
        {
            if (configurations.Count > 0)
            {
                // Create a custom dropdown with checkboxes
                string currentName = selectedConfigIndex < configurations.Count ? configurations[selectedConfigIndex].name : "Select...";

                if (EditorGUILayout.DropdownButton(new GUIContent(currentName), FocusType.Keyboard, GUILayout.ExpandWidth(true)))
                {
                    var menu = new GenericMenu();

                    for (int i = 0; i < configurations.Count; i++)
                    {
                        int index = i; // Capture for closure
                        var config = configurations[i];
                        string menuName = config.enabled ? $"✓ {config.name}" : $"   {config.name}";

                        menu.AddItem(new GUIContent(menuName), selectedConfigIndex == i, () => {
                            selectedConfigIndex = index;
                        });
                    }

                    menu.AddSeparator("");
                    menu.AddItem(new GUIContent("Toggle All On"), false, () => {
                        foreach (var config in configurations)
                            config.enabled = true;
                    });
                    menu.AddItem(new GUIContent("Toggle All Off"), false, () => {
                        foreach (var config in configurations)
                            config.enabled = false;
                    });

                    menu.ShowAsContext();
                }

                // Add checkbox for the selected configuration
                if (selectedConfigIndex < configurations.Count)
                {
                    configurations[selectedConfigIndex].enabled = EditorGUILayout.Toggle(configurations[selectedConfigIndex].enabled, GUILayout.Width(20));
                }
            }
            else
            {
                EditorGUILayout.LabelField("No runs :[", GUILayout.ExpandWidth(true));
            }

            using (new EditorGUI.DisabledScope(configurations.Count == 0))
            {
                if (GUILayout.Button("-", GUILayout.Width(30)))
                {
                    configurations.RemoveAt(selectedConfigIndex);
                    selectedConfigIndex = Mathf.Clamp(selectedConfigIndex, 0, configurations.Count - 1);
                }
            }
        }
        EditorGUILayout.EndHorizontal();
    }

    private void DrawActionButtons()
    {
        if (GUILayout.Button("Save Configuration"))
        {
            SaveConfiguration();
        }

        if (GUILayout.Button("Load Configuration"))
        {
            LoadConfiguration();
        }

        if (GUILayout.Button("Clear Configuration"))
        {
            ClearConfiguration();
        }
    }

    #region Core Functionality

    private void SaveConfiguration()
    {
        string path = EditorUtility.SaveFilePanel("Save Configurations", c_ConfigFolder, "kitbash_default", "json");
        if (!string.IsNullOrEmpty(path))
        {
            var wrapper = new ConfigurationListWrapper { configurations = configurations };
            string json = JsonUtility.ToJson(wrapper, true);
            File.WriteAllText(path, json);
            AssetDatabase.Refresh();
        }
    }

    private void LoadConfiguration()
    {
        string path = EditorUtility.OpenFilePanel("Load Configurations", c_ConfigFolder, "json");
        if (!string.IsNullOrEmpty(path))
        {
            string json = File.ReadAllText(path);
            var wrapper = JsonUtility.FromJson<ConfigurationListWrapper>(json);
            configurations = wrapper.configurations;
            selectedConfigIndex = 0;

            // Ensure all loaded configurations are enabled by default if the JSON doesn't contain enabled field
            foreach (var config in configurations)
            {
                if (!json.Contains($"\"enabled\""))
                    config.enabled = true;
            }
        }
    }

    private void ClearConfiguration()
    {
        configurations = new();
        selectedConfigIndex = 0;
    }

    private void RunAllConfigurations()
    {
        if (isRunning)
        {
            EditorUtility.DisplayDialog("Already Running",
                "An operation is already in progress. Use Force Stop if it's stuck.", "OK");
            return;
        }

        foreach (var config in configurations)
        {
            if (config.enabled && !ValidateConfiguration(config))
            {
                EditorUtility.DisplayDialog("Invalid Run", $"Run '{config.name}' has invalid settings", "OK");
                return;
            }
        }

        currentOperation = EditorCoroutineUtility.StartCoroutine(RunAll(configurations), this);
    }

    private IEnumerator RunAll(List<Configuration> _config)
    {
        isRunning = true;
        AssetDatabase.SaveAssets();
        InitializeOutputFolder(out string outputFolder);
        var masterRep = InitializeMasterReport();

        var validInputFolders = CollectValidInputFolders(_config);
        CleanupOrphanedOutputFolders(_config, validInputFolders, masterRep);

        var splitConfigs = CreateSplitConfigurations(_config);
        yield return ProcessAllConfigurations(splitConfigs, masterRep);

        File.WriteAllText($"{outputFolder}/MasterReport.md", masterRep.ToString());
        isRunning = false;
    }

    private void InitializeOutputFolder(out string outputFolder)
    {
        outputFolder = $"{Application.persistentDataPath}/PrefabOptimiser";
        if (Directory.Exists(outputFolder))
            Directory.Delete(outputFolder, true);
        Directory.CreateDirectory(outputFolder);
    }

    private ReportHelper InitializeMasterReport()
    {
        var masterRep = new ReportHelper();
        masterRep.AppendLine("Prefab Optimisation Overview", 1);
        masterRep.AppendLine("Sub-reports:", 2);
        masterRep.BulletLevel++;
        return masterRep;
    }

    private HashSet<string> CollectValidInputFolders(List<Configuration> configs)
    {
        var validInputFolders = new HashSet<string>();

        foreach (var config in configs)
        {
            if (!config.enabled || !Directory.Exists(config.directoryFrom))
                continue;

            if (config.superFolder)
            {
                var subFolders = Directory.GetDirectories(config.directoryFrom);
                foreach (var subFolder in subFolders)
                {
                    validInputFolders.Add(NormalizePath(subFolder));
                }
            }
            else
            {
                validInputFolders.Add(NormalizePath(config.directoryFrom));
            }
        }

        return validInputFolders;
    }

    private void CleanupOrphanedOutputFolders(List<Configuration> configs, HashSet<string> validInputFolders,
        ReportHelper masterRep)
    {
        foreach (var config in configs)
        {
            if (!Directory.Exists(config.directoryTo))
                continue;

            if (config.superFolder)
                CleanupSuperFolderOutputs(config, validInputFolders, masterRep);
            else
                CleanupSingleFolderOutput(config, validInputFolders, masterRep);
        }
    }

    private void CleanupSuperFolderOutputs(Configuration config, HashSet<string> validInputFolders,
        ReportHelper masterRep)
    {
        var outputSubFolders = Directory.GetDirectories(config.directoryTo);
        foreach (var outputSubFolder in outputSubFolders)
        {
            string normalizedOutputPath = NormalizePath(outputSubFolder);
            string inputFolderName = Path.GetFileName(normalizedOutputPath);
            string expectedInputPath = NormalizePath(Path.Combine(config.directoryFrom, inputFolderName));

            if (!validInputFolders.Contains(expectedInputPath))
            {
                var relPath = GetRelativePath(normalizedOutputPath);
                AssetDatabase.DeleteAsset(relPath);
                masterRep.AppendLine($"Removed orphaned output folder: {normalizedOutputPath}", 3);
            }
        }
    }

    private void CleanupSingleFolderOutput(Configuration config, HashSet<string> validInputFolders,
        ReportHelper masterRep)
    {
        if (!validInputFolders.Contains(NormalizePath(config.directoryFrom)))
        {
            Directory.Delete(config.directoryTo, true);
            masterRep.AppendLine($"Removed orphaned output folder: {config.directoryTo}", 3);
        }
    }

    private List<Configuration> CreateSplitConfigurations(List<Configuration> configs)
    {
        var splitConfigs = new List<Configuration>();

        foreach (var config in configs)
        {
            if (!config.enabled || !Directory.Exists(config.directoryFrom))
                continue;

            if (config.superFolder)
            {
                var subFolders = Directory.GetDirectories(config.directoryFrom);
                foreach (var subFolder in subFolders)
                {
                    var newConfig = new Configuration(
                        subFolder,
                        $"{config.directoryTo}/{Path.GetFileName(subFolder)}",
                        false
                    );
                    newConfig.enabled = config.enabled; // Inherit enabled state
                    splitConfigs.Add(newConfig);
                }
            }
            else
                splitConfigs.Add(config);
        }

        return splitConfigs;
    }

    private IEnumerator ProcessAllConfigurations(List<Configuration> splitConfigs, ReportHelper masterRep)
    {
        List<(Configuration, KitBashOptimiseManager)> managers = new();

        foreach (var config in splitConfigs)
        {
            var manager = Run(config, forceUpdate);
            managers.Add((config, manager));

            if (manager != null)
            {
                var asyncOp = Resources.UnloadUnusedAssets();
                while (!asyncOp.isDone)
                    yield return null;
            }
        }

        WriteConfigurationResults(managers, masterRep);
    }

    private void WriteConfigurationResults(List<(Configuration, KitBashOptimiseManager)> managers,
        ReportHelper masterRep)
    {
        int RendCount(KitBashOptimiseManager m) => m?.m_currentRendCount ?? 0;
        managers.Sort((x, y) => RendCount(y.Item2).CompareTo(RendCount(x.Item2)));

        foreach (var (config, manager) in managers)
        {
            if (manager == null)
            {
                masterRep.AppendLine(
                    $"{Path.GetFileName(config.directoryFrom)} skipped because destination is newer", 3);
            }
            else
            {
                WriteManagerResults(config, manager, masterRep);
            }
        }
    }

    private void WriteManagerResults(Configuration config, KitBashOptimiseManager manager, ReportHelper masterRep)
    {
        masterRep.AppendLine(
            $"[{Path.GetFileName(config.directoryFrom)}](./Reports/{Path.GetFileName(config.directoryTo)}.md)", 3);
        masterRep.BulletLevel++;
        masterRep.AppendLine($"Time: {manager.m_time:F2}s");
        masterRep.AppendLine($"Materials: {manager.m_originalMatCount} -> {manager.m_currentMatCount}");
        masterRep.AppendLine($"Renderers: {manager.m_originalRendCount} -> {manager.m_currentRendCount}");
        masterRep.BulletLevel--;
    }

    private string NormalizePath(string path) => path.Replace('\\', '/');

    private bool ValidateConfiguration(Configuration config)
    {
        bool foldersValid = ValidateDirectories(config, out bool folderEmpty);

        return foldersValid;
    }

    #endregion

    #region Reusable UI Elements

    private void DrawRunAllButton()
    {
        bool playing = Application.isPlaying;
        bool anyEnabledConfigs = configurations.Any(c => c.enabled);
        bool allValid = anyEnabledConfigs;
        foreach (var config in configurations)
        {
            if (config.enabled)
                allValid &= ValidateConfiguration(config);
        }

        using (new EditorGUI.DisabledScope(!allValid || playing || isRunning))
        {
            GUI.backgroundColor = allValid ? new Color(0.1f, 0.6f, 0.1f) : Color.gray;
            if (GUILayout.Button("Optimise All!", GetActionButtonStyle()))
            {
                RunAllConfigurations();
            }

            GUI.backgroundColor = Color.white;
        }

        if (!anyEnabledConfigs)
        {
            EditorGUILayout.HelpBox("No enabled runs to run", MessageType.Error);
        }
        else if (!allValid)
        {
            EditorGUILayout.HelpBox("One or more enabled runs are invalid", MessageType.Error);
        }
        else if (playing)
        {
            EditorGUILayout.HelpBox("Cannot run while in play mode", MessageType.Error);
        }
        else if (isRunning)
        {
            EditorGUILayout.HelpBox("Running...", MessageType.Info);
        }
    }

    private void DrawConfigurationSection(Configuration config)
    {
        configSectionExpanded = EditorGUILayout.BeginFoldoutHeaderGroup(configSectionExpanded, "Configure Pass:");
        if (configSectionExpanded)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.BeginVertical(GUI.skin.box);
            {
                DrawDirectoryGroup(config);
                EditorGUILayout.Space(10);
                DrawAtlasSection("Atlas Name", ref config.atlasName);
                EditorGUILayout.Space(10);
                DrawAtlasStrategySection(ref config.superFolder);
                EditorGUILayout.Space(10);
                DrawDirectoryValidation(config);
            }
            EditorGUILayout.EndVertical();
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.EndFoldoutHeaderGroup();
    }

    private void DrawAtlasSection(string label, ref string atlasName)
    {
        EditorGUILayout.LabelField(label, EditorStyles.boldLabel);
        EditorGUILayout.Space(3);
        atlasName = EditorGUILayout.TextField(atlasName);
    }

    private void DrawAtlasStrategySection(ref bool superFolder)
    {
        EditorGUILayout.BeginVertical(GUI.skin.box);
        {
            EditorGUILayout.HelpBox(
                superFolder ? "Each subfolder will be atlassed separately" : "Entire folder will be atlassed together",
                MessageType.Info);

            EditorGUILayout.BeginHorizontal();
            {
                superFolder = EditorGUILayout.Toggle(superFolder, GUILayout.Width(20));
                EditorGUILayout.LabelField("Process Subfolders Separately", GUILayout.ExpandWidth(true));
            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndVertical();
    }

    private void DrawDirectoryGroup(Configuration config)
    {
        DrawDirectorySection("Input Directory (Unoptimised)", ref config.directoryFrom);
        EditorGUILayout.Space(10);
        DrawDirectorySection("Output Directory (Optimised)", ref config.directoryTo);
    }

    private void DrawDirectoryValidation(Configuration config)
    {
        bool foldersValid = ValidateDirectories(config, out bool folderEmpty);

        if (!folderEmpty && !foldersValid)
        {
            EditorGUILayout.HelpBox("Directories must be completely separate locations.", MessageType.Error);
        }
    }

    private bool ValidateDirectories(Configuration config, out bool folderEmpty)
    {
        folderEmpty = string.IsNullOrEmpty(config.directoryFrom) || string.IsNullOrEmpty(config.directoryTo);
        return !folderEmpty &&
               !config.directoryTo.StartsWith($"{config.directoryFrom}/") &&
               !config.directoryFrom.StartsWith($"{config.directoryTo}/");
    }

    private void DrawDirectorySection(string header, ref string directory)
    {
        EditorGUILayout.LabelField(header, EditorStyles.miniBoldLabel);
        EditorGUILayout.Space(3);
        EditorGUILayout.HelpBox("Drag folder from Project window here", MessageType.None);
        DrawDragAndDropArea(ref directory);
    }

    private void DrawHorizontalLine()
    {
        Rect rect = GUILayoutUtility.GetRect(1, 1);
        rect.xMin = 0;
        rect.xMax = EditorGUIUtility.currentViewWidth;
        EditorGUI.DrawRect(rect, new Color(0.4f, 0.4f, 0.4f, 0.5f));
    }

    private void DrawToggleField(ref bool value, string label)
    {
        EditorGUILayout.BeginHorizontal();
        {
            EditorGUILayout.LabelField(label, EditorStyles.boldLabel, GUILayout.Width(150));

            EditorGUILayout.BeginHorizontal(GUILayout.Width(30));
            value = EditorGUILayout.Toggle(value);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void DrawDragAndDropArea(ref string directory)
    {
        Rect dropArea = GUILayoutUtility.GetRect(0.0f, 50.0f, GUILayout.ExpandWidth(true));
        Rect indentedDropArea = EditorGUI.IndentedRect(dropArea); // Apply indentation

        GUIStyle style = new GUIStyle(GUI.skin.box)
        {
            normal = { textColor = Color.white },
            alignment = TextAnchor.MiddleCenter
        };
        GUI.Box(indentedDropArea,
            string.IsNullOrEmpty(directory) ? "Drag & Drop Folder Here" : directory, style);

        Color borderColor = Color.grey;
        DrawBorder(indentedDropArea, borderColor, 2); // Use indented rect for border

        Event evt = Event.current;
        if (indentedDropArea.Contains(evt.mousePosition)) // Check against indented rect
        {
            if (evt.type == EventType.DragUpdated)
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                evt.Use();
            }
            else if (evt.type == EventType.DragPerform)
            {
                DragAndDrop.AcceptDrag();
                if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
                {
                    string path = DragAndDrop.paths[0];
                    if (AssetDatabase.IsValidFolder(path) || AssetDatabase.AssetPathExists(path))
                    {
                        directory = path;
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("Invalid Path",
                            "Please drop a valid folder or file from the Project window.", "OK");
                    }
                }

                evt.Use();
            }
        }
    }

    private void DrawBorder(Rect rect, Color color, float thickness)
    {
        (color, GUI.color) = (GUI.color, color);

        GUI.DrawTexture(new Rect(rect.x, rect.y, rect.width, thickness), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.x, rect.yMax - thickness, rect.width, thickness), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.x, rect.y, thickness, rect.height), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.xMax - thickness, rect.y, thickness, rect.height), Texture2D.whiteTexture);

        GUI.color = color;
    }

    #endregion

    #region Style Providers

    private GUIStyle GetHeaderStyle() => new(EditorStyles.largeLabel)
    {
        fontSize = 18,
        fontStyle = FontStyle.Bold,
        alignment = TextAnchor.MiddleCenter
    };

    private GUIStyle GetActionButtonStyle() => new(GUI.skin.button)
    {
        fontSize = 14,
        fontStyle = FontStyle.Bold,
        padding = new RectOffset(20, 20, 8, 8)
    };

    #endregion

    private void OnDisable()
    {
        ForceStop();
    }
}