using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.Serialization;

/// <summary>
/// Tracks dependencies for KitBash optimization process.
/// Records file paths and access times for meshes, materials, and textures.
/// Uses local path format: Assets/... for external assets, /... for assets inside the operating folder.
/// The operating folder is determined dynamically based on the output directory.
/// </summary>
[Serializable]
public class DependencyTracker
{
    private const string DEPENDENCY_FILE_NAME = "dependencies.json";

    [Serializable]
    public class DependencyInfo
    {
        public string filePath;
        public string lastAccessTime;
        public string assetType;

        public DependencyInfo(string path, DateTime accessTime, string type)
        {
            filePath = path;
            lastAccessTime = accessTime.ToString("o"); // ISO 8601 format
            assetType = type;
        }
    }

    [Serializable]
    private class DependencyData
    {
        public string lastWriteTime;
        public List<DependencyInfo> dependencies = new();
    }

    private Dictionary<string, DependencyInfo> m_dependencies = new();
    private string m_outputDirectory;
    private string m_operatingFolder;
    private string m_lastWriteTime;

    public DependencyTracker(string outputDirectory, bool loadPrevious)
    {
        m_outputDirectory = outputDirectory;

        // Convert output directory to relative path format for use as operating folder
        string normalizedOutputDir = outputDirectory.Replace('\\', '/');
        string projectRoot = Path.Combine(Application.dataPath, "..").Replace('\\', '/');
        m_operatingFolder = normalizedOutputDir.Replace(projectRoot + "/", "");

        if (loadPrevious)
            LoadDependencies();
    }
    
    /// <summary>
    /// Tracks a dependency file with its current access time
    /// </summary>
    public void TrackDependency(string filePath, string assetType)
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            return;

        var accessTime = File.GetLastWriteTime(filePath);
        var localPath = ConvertToLocalPath(filePath);

        if (m_dependencies.ContainsKey(localPath))
        {
            m_dependencies[localPath] = new DependencyInfo(localPath, accessTime, assetType);
        }
        else
        {
            m_dependencies.Add(localPath, new DependencyInfo(localPath, accessTime, assetType));
        }
    }
    
    /// <summary>
    /// Tracks a Unity asset dependency with its current access time
    /// </summary>
    public void TrackAssetDependency(UnityEngine.Object asset, string assetType)
    {
        if (asset == null)
            return;

        string assetPath = AssetDatabase.GetAssetPath(asset);
        if (string.IsNullOrEmpty(assetPath))
            return;

        // Convert Unity asset path directly to local path format
        var localPath = ConvertAssetPathToLocalPath(assetPath);
        var fullPath = Path.Combine(Application.dataPath, "..", assetPath);

        if (!File.Exists(fullPath))
            return;

        var accessTime = File.GetLastWriteTime(fullPath);

        if (m_dependencies.ContainsKey(localPath))
        {
            m_dependencies[localPath] = new DependencyInfo(localPath, accessTime, assetType);
        }
        else
        {
            m_dependencies.Add(localPath, new DependencyInfo(localPath, accessTime, assetType));
        }
    }
    
    /// <summary>
    /// Saves the dependency information to a file
    /// </summary>
    public void SaveDependencies()
    {
        var data = new DependencyData
        {
            lastWriteTime = DateTime.UtcNow.ToString("o"), // ISO 8601 format
            dependencies = m_dependencies.Values.ToList()
        };
        
        string json = JsonUtility.ToJson(data, true);
        string filePath = Path.Combine(m_outputDirectory, DEPENDENCY_FILE_NAME);
        
        try
        {
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            File.WriteAllText(filePath, json);
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save dependency information: {e.Message}");
        }
    }
    
    /// <summary>
    /// Loads dependency information from a file
    /// </summary>
    private void LoadDependencies()
    {
        string filePath = Path.Combine(m_outputDirectory, DEPENDENCY_FILE_NAME);
        
        if (!File.Exists(filePath))
            return;
            
        try
        {
            string json = File.ReadAllText(filePath);
            var data = JsonUtility.FromJson<DependencyData>(json);
            m_lastWriteTime = data.lastWriteTime;
            m_dependencies.Clear();
            foreach (var dep in data.dependencies)
                m_dependencies[dep.filePath] = dep;
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to load dependency information: {e.Message}");
        }
    }
    
    /// <summary>
    /// Checks if any dependencies have changed since they were last tracked
    /// </summary>
    public bool HaveDependenciesChanged(out DateTime _lastWriteTime)
    {
        if (DateTime.TryParse(m_lastWriteTime, out DateTime dt))
            _lastWriteTime = dt;
        else
        {
            _lastWriteTime = DateTime.MinValue;
            return true;
        }

        foreach (var dep in m_dependencies.Values)
        {
            // Convert local path back to absolute path for file system operations
            string absolutePath = ConvertLocalPathToAbsolute(dep.filePath);
            if (!File.Exists(absolutePath))
                continue;

            DateTime lastAccessTime = DateTime.Parse(dep.lastAccessTime);
            DateTime currentTime = File.GetLastWriteTime(absolutePath);

            if (currentTime > lastAccessTime)
            {
                Debug.Log($"Dependency changed: {dep.filePath} ({dep.assetType})");
                return true;
            }
        }

        return false;
    }
    
    /// <summary>
    /// Gets a report of all tracked dependencies
    /// </summary>
    public void WriteDependencyReport(ReportHelper report)
    {
        report.AppendLine("External Dependencies:", 2);
        report.BulletLevel++;

        var groupedDeps = m_dependencies.Values
            .GroupBy(d => d.assetType)
            .OrderBy(g => g.Key);

        foreach (var group in groupedDeps)
        {
            report.AppendLine($"{group.Key}s: {group.Count()}");
            report.BulletLevel++;

            foreach (var dep in group.OrderBy(d => d.filePath))
            {
                // Dependencies are already in local path format
                DateTime lastAccessTime = DateTime.Parse(dep.lastAccessTime);
                report.AppendLine($"{dep.filePath} (Last accessed: {lastAccessTime:g})");
            }

            report.BulletLevel--;
        }

        report.BulletLevel--;
    }

    /// <summary>
    /// Converts an absolute file path to local path format
    /// </summary>
    private string ConvertToLocalPath(string absolutePath)
    {
        // Normalize path separators
        string normalizedPath = absolutePath.Replace('\\', '/');

        // Get the project root path
        string projectRoot = Path.Combine(Application.dataPath, "..").Replace('\\', '/');

        // Remove project root and "../" patterns from the path
        string cleanPath = normalizedPath.Replace(projectRoot + "/Assets/../", "");

        // Check if the path is inside the operating folder
        if (cleanPath.StartsWith(m_operatingFolder))
        {
            // For paths inside the operating folder, make them relative to the operating folder with leading slash
            string relativePath = cleanPath.Substring(m_operatingFolder.Length);
            if (!relativePath.StartsWith("/"))
            {
                relativePath = "/" + relativePath;
            }
            return relativePath;
        }
        else
        {
            // For paths outside the operating folder, return the clean path starting with Assets/
            return cleanPath;
        }
    }

    /// <summary>
    /// Converts a Unity asset path to local path format
    /// </summary>
    private string ConvertAssetPathToLocalPath(string assetPath)
    {
        // Normalize path separators
        string normalizedPath = assetPath.Replace('\\', '/');

        // Check if the path is inside the operating folder
        if (normalizedPath.StartsWith(m_operatingFolder))
        {
            // For paths inside the operating folder, make them relative to the operating folder with leading slash
            string relativePath = normalizedPath.Substring(m_operatingFolder.Length);
            if (!relativePath.StartsWith("/"))
            {
                relativePath = "/" + relativePath;
            }
            return relativePath;
        }
        else
        {
            // For paths outside the operating folder, return the asset path as-is
            return normalizedPath;
        }
    }

    /// <summary>
    /// Converts a local path back to absolute path for file system operations
    /// </summary>
    private string ConvertLocalPathToAbsolute(string localPath)
    {
        string projectRoot = Path.Combine(Application.dataPath, "..").Replace('\\', '/');

        if (localPath.StartsWith("/"))
        {
            // Path inside operating folder - prepend operating folder path
            return Path.Combine(projectRoot, m_operatingFolder + localPath).Replace('\\', '/');
        }
        else
        {
            // External path - prepend project root
            return Path.Combine(projectRoot, localPath).Replace('\\', '/');
        }
    }
}
