using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public interface INavigator {
	Vector3 Target(int _context);
	IEnumerator OnArrive(NavAgent _who, int _context);
	bool OnLeave(NavAgent _who, INavigator _finalDestination); // leaving a navigator for another navigator - can be failed
	void OnLeave(NavAgent _who, Vector3 _finalDestination);
}

public class WorkerQueue
{
	private static List<WorkerQueue> s_allWorkerQueues = new();

	private static void Register(WorkerQueue _this) { s_allWorkerQueues.Add(_this); }
	private static void Unregister(WorkerQueue _this) { s_allWorkerQueues.Remove(_this); }

	public static void Refresh(PathManager.Path _which)
	{
		foreach (var q in s_allWorkerQueues)
			if (q.m_path == _which)
				q.Refresh();
	}

	public BuildingNav m_owner;
	public PathManager.Path m_path;
	public float m_pathDoorPosition, m_pathToDoorPos;
	private Vector3 m_currentOwnerExterior;

	public WorkerQueue(BuildingNav _nav)
	{
		m_owner = _nav;
		Refresh();
		Register(this);
	}
	~WorkerQueue() {
		FLog.Log("WorkerQueue");
		try {
			Unregister(this);
		} catch (System.Exception e) {}
	}
	private void Refresh()
	{
		m_currentOwnerExterior = m_owner.DoorExterior;
		const float c_maxDoorToRoadDistance = 6;
		var origin = m_owner.DoorInterior;
		var direction = m_owner.DoorExterior - m_owner.DoorInterior;
		(m_path, m_pathDoorPosition) = RoadManager.Me.m_pathSet.FindPathIntersection(origin, direction, c_maxDoorToRoadDistance);
		if (m_path == null)
		{
			(m_path, m_pathDoorPosition) = RoadManager.Me.m_pathSet.FindPathIntersection(origin, direction - m_owner.transform.right, c_maxDoorToRoadDistance);
			if (m_path == null)
			{
				(m_path, m_pathDoorPosition) = RoadManager.Me.m_pathSet.FindPathIntersection(origin, direction + m_owner.transform.right, c_maxDoorToRoadDistance);
			}
		}
		if (m_path != null)
		{
			var (pos, fwd, side) = m_path.GetPosFwdSide(m_pathDoorPosition);
			m_pathToDoorPos = Vector3.Dot(m_owner.DoorExterior - (Vector3) pos, side);
		}
	}

	public void CheckAndRefresh()
	{
		if ((m_currentOwnerExterior - m_owner.DoorExterior).xzSqrMagnitude() > .001f * .001f)
			Refresh();
	}

	public (Vector3, Vector3) Position(int _queueIndex, float _towardsDoor = 1)
	{
		if (m_path == null) return (Vector3.zero, Vector3.forward);
		float sign = m_pathDoorPosition > .5f ? -1 : 1;
		var indexStep = m_path.LocStep * 8f * sign;
		var (pos, fwd, side) = m_path.GetPosFwdSide(m_pathDoorPosition + (_queueIndex + 1) * indexStep);
		return (pos + side * (m_pathToDoorPos * _towardsDoor), fwd * -sign);
	}

	public Vector3 GetPathPosition(bool _refreshFirst = false)
	{
		if (_refreshFirst) Refresh();
		if (m_path == null) return Vector3.zero;
		return m_path.GetPoint(m_pathDoorPosition);
	}
}

public class BuildingNav : MonoBehaviour, INavigator {
	InteractTransform m_door;
	void Awake() {
		GlobalData.Me.m_buildings.Add(this);
		MoveBuilding();
	}
	public bool m_ignoreCalculateExitPos = false;
	public Vector3 DoorExterior;
	public Vector3 DoorInterior;
	
	struct BestResult
	{
		public BCEntrance entrance;
		public BCActionBase mainAction;
		public Block bestBlock;
	}
	
	private bool m_isFakeBuilding; public bool IsFakeBuilding => m_isFakeBuilding;

	public void SetFakeBuilding()
	{
		m_isFakeBuilding = true;
		MoveBuilding();
	}

	private void RefreshQueue()
	{
		if (m_queue == null)
			m_queue = new WorkerQueue(this);
		else
			m_queue.CheckAndRefresh();
	}

	private bool m_hasDoorInteract = false;
	HashSet<int> m_noDoorComplaints = new();
	public void MoveBuilding()
	{
		if (Utility.IsShuttingDown) return;

		if (m_isFakeBuilding)
		{
			DoorInterior = DoorExterior = transform.position + (-transform.right * 1.2f);
			RefreshQueue();
			return;
		}

		m_door = InteractTransform.Find(gameObject, InteractTransform.Type.Door, transform.forward);
		if (m_door == null) m_door = InteractTransform.Find(gameObject, InteractTransform.Type.SecondaryDoor);
		if (m_door == null)
		{
			Vector3 sumPos = Vector3.zero;
			float totalBlocks = 0;
			Block primary = null, secondary = null, tertiary = null;
			foreach (var block in GetComponentsInChildren<Block>())
			{
				sumPos += block.transform.position;
				totalBlocks += 1;
				var entrance = block.GetComponentInChildren<BCEntrance>();
				var action = block.GetComponentInChildren<BCActionBase>();
				if (entrance != null && action != null) primary = block;
				else if (entrance != null) secondary = block;
				else if (action != null) tertiary = block;
			}
			if (primary == null) primary = secondary;
			if (primary == null) primary = tertiary;
			if (primary != null)
				DoorInterior = primary.transform.position.GroundPosition();
			else if (totalBlocks > 0)
				DoorInterior = (sumPos / totalBlocks).GroundPosition();
			else
				DoorInterior = transform.position.GroundPosition();
			m_hasDoorInteract = false;
		}
		else
		{
			DoorInterior = m_door.transform.position;
			m_hasDoorInteract = true;
		}
		var blockToDoor = DoorInterior - transform.position;
		blockToDoor = transform.forward * Vector3.Dot(blockToDoor, transform.forward);
		
		InteractTransform doorOuterPosition = InteractTransform.Find(m_door?.transform.parent.gameObject ?? gameObject, InteractTransform.Type.DoorFront);
		if (doorOuterPosition != null)
			DoorExterior = doorOuterPosition.transform.position;
		else if (m_door != null)
			DoorExterior = DoorInterior + m_door.transform.forward * m_innerToOuterDistance * TerrainBlock.GlobalScale;
		else
			DoorExterior = DoorInterior - blockToDoor - transform.forward * m_innerToOuterDistance * TerrainBlock.GlobalScale;

		if (!m_ignoreCalculateExitPos)
		{
			CalculateExitPos();
			DoorExterior = m_exitPosition + (m_exitPosition - DoorInterior).normalized;
		}

		RefreshQueue();
		PathManager.s_blockDataDirty = true;
		
		foreach (var switchOnBedrock in GetComponentsInChildren<SwitchOnBedrockType>())
			switchOnBedrock.Switch();
	}

	Vector3 m_exitPosition;
	float m_exitDistance;
	void CalculateExitPos()
	{
		Transform tr = transform;
		var _pos = tr.position;
		var _rot = tr.eulerAngles.y;
		var _doorCenter = (DoorInterior + DoorExterior) * .5f;
		var doorFwd = (DoorExterior - DoorInterior).GetXZNorm();
		var finalWidth = m_navDataPreviousW * BuildingPlacementManager.c_buildingBlockSize;
		var finalDepth = m_navDataPreviousH * BuildingPlacementManager.c_buildingBlockSize;
		var (pos, edge1, edge2) = PathManager.Path.GetEdgesFromNavData(_pos, _rot.EulerY(), ref finalWidth, ref finalDepth, true, PathManager.c_buildingMargin);
		var doorIntersect = PathManager.RayIntersectsQuadrClipped(pos, edge1, edge2, _doorCenter, doorFwd);
		var toDoor = doorIntersect - _doorCenter;
		var toDoorDistance = Mathf.Ceil(Vector3.Dot(toDoor, doorFwd)) + 1;
		m_exitPosition = doorIntersect;
		m_exitDistance = toDoorDistance;
	}

	WorkerQueue m_queue;
	public float m_innerToOuterDistance = 4f;
	public Vector3 GetPathPos()
	{
		return m_queue.GetPathPosition(true);
	}

	public Vector3 HeightReferencePoint => m_exitPosition.GroundPosition();//m_hasDoorInteract ? (DoorExterior + (DoorExterior - DoorInterior) * .5f).GroundPosition() : transform.position.GroundPosition(_originalHeight: true);
	public float GetDesiredBuildingHeight()
	{
		//float y = GlobalData.Me.GetRealHeight(GetPathPos());
		float y = HeightReferencePoint.y;
		var set = m_queue.m_path?.Set;
		//var ng = GetComponent<NGCommanderBase>();
		//var index = m_queue.m_path == null ? -1 : GameManager.Me.m_state.m_paths.IndexOf(m_queue.m_path);
		//Debug.LogError($"GDBH {ng.name} {ng.m_linkUID} @ {transform.position} {y} @ {GetPathPos()}  + {set?.m_buildingYAdjust ?? 0} path {index}");
		return y + (set?.m_buildingYAdjust ?? 0);
	}

	public bool HasValidPath => m_queue.m_path != null && m_queue.m_path.Count > 0;
	
	public Vector3 GetPathPosAtIndex(int _i, float _offsetTowardsDoor)
	{
		(Vector3 one, Vector3 two) = m_queue.Position(_i, _offsetTowardsDoor);
		return one;
	}

	public Vector3 Target(int _context) { return _context == 0 ? DoorExterior : DoorInterior; }
	public IEnumerator OnArrive(NavAgent _who, int _context) {
		if (_context == 0) {
			_who.NavigateToDirect(this, 0.5f, 1);
			_who.m_body.isKinematic = true;
		} else {
			_who.transform.GetChild(0).gameObject.SetActive(false);
		}
		yield return null;
	}
	public bool OnLeave(NavAgent _who, INavigator _finalDestination) {
		return _finalDestination != (INavigator)this;
	}
	public void OnLeave(NavAgent _who, Vector3 _finalDestination) {
		_who.transform.GetChild(0).gameObject.SetActive(true);
		_who.m_body.isKinematic = false;
		_who.transform.position = (DoorInterior + DoorExterior) * .5f + new Vector3(Random.Range(-.2f,.2f), 0, Random.Range(-.2f,.2f));
	}

	int m_navDataPreviousW = -1, m_navDataPreviousH = -1;
	float m_navDataPreviousRot = -1;
	Vector3 m_navDataPreviousPos, m_navDataPreviousExit;

	public (int, int) GetPreviousNavigationSize() => (m_navDataPreviousW, m_navDataPreviousH);
	
	public (Vector3, Vector3) RemovePreviousNavigationData()
	{
		if (m_navDataPreviousW > -1)
			return FillNavigationData((int) GlobalData.NavCostTypes.OffRoad, GlobalData.c_terrainMin, GlobalData.c_terrainMax, false, true);
		return (Vector3.one, Vector3.zero);
	}

	public void RefreshNavigationDetails(int _w, int _h)
	{
		MoveBuilding();
		Transform tr = transform;
		m_navDataPreviousPos = tr.position;
		m_navDataPreviousRot = tr.eulerAngles.y;
		m_navDataPreviousW = _w;
		m_navDataPreviousH = _h;
		m_navDataPreviousExit = m_exitPosition;
	}

	public (Vector3, Vector3) UpdateNavigationData(int _w, int _h) {
		if (_w == 0) (_w, _h) = (m_navDataPreviousW, m_navDataPreviousH);
		RemovePreviousNavigationData();
		RefreshNavigationDetails(_w, _h);
		return FillNavigationData((int)GlobalData.NavCostTypes.Building, GlobalData.c_terrainMin, GlobalData.c_terrainMax);
	}
	
	public (Vector3, Vector3) FillNavigationData(int _value, Vector3 _dirtyMin, Vector3 _dirtyMax, bool _fillDoorRoute = true, bool _ignorePriorities = false)
	{
		return PathManager.FillNavBuilding(m_navDataPreviousPos, m_navDataPreviousRot, m_navDataPreviousW, m_navDataPreviousH, _value, _fillDoorRoute, m_navDataPreviousExit, _dirtyMin, _dirtyMax, _ignorePriorities);
	}

	public void FillSplatData(bool _innerSplat)
	{
		if (!_innerSplat) return; // not necessary due to building bases, use inner splat to remove grass
		int splat = _innerSplat ? GlobalData.Me.m_splatUnderBuildingInner : GlobalData.Me.m_splatUnderBuilding;
		float radiusBase = _innerSplat ? 5f : 6f;
		float radiusRnd = _innerSplat ? .5f : 1;
		if (splat != -1)
		{
			var cmd = GetComponent<NGCommanderBase>();
			Block first = null;
			if (cmd != null && cmd.Visuals != null && cmd.Visuals.childCount > 0)
				first = cmd.Visuals.GetChild(0).GetComponent<Block>();
			if (first != null && first.name.StartsWith("MABase"))
				foreach (Transform plot in first.m_toHinges)
				{
					if (plot.gameObject.activeSelf && plot.transform.forward.y > 0)
					{
						uint seed = (uint) plot.GetInstanceID();
						var rnd = Utility.XorShift01(ref seed);
						if (_innerSplat && splat != -2)
							CameraRenderSettings.Me.SetSplatCircle(plot.position, radiusBase + rnd * radiusRnd, splat, .7f);
						else
							CameraRenderSettings.Me.SetSplatCircleBedrock(plot.position, radiusBase + rnd * radiusRnd, .7f);
					}
				}

			//PathManager.FillSplatRectangle(m_navDataPreviousPos, m_navDataPreviousRot, m_navDataPreviousW * 4, m_navDataPreviousH * 4, GlobalData.Me.m_splatUnderBuilding, GlobalData.Me.m_splatMargin);
		}
	}

	void OnDestroy() {
		if (GlobalData.Me != null && RoadManager.Me != null)
		{
			GlobalData.Me.m_buildings.Remove(this);
			RemovePreviousNavigationData();
		}
	}
	
#if UNITY_EDITOR
	void OnDrawGizmos() {
		if (Utility.IsShuttingDown) return;
		Gizmos.color = Color.blue;
		const float c_raise = .2f;
		var doorCenter = ((DoorInterior + DoorExterior) * .5f).GroundPosition(c_raise);
		Gizmos.DrawSphere(DoorExterior.GroundPosition(c_raise), .15f);
		Gizmos.color = Color.green;
		Gizmos.DrawSphere(DoorInterior.GroundPosition(c_raise), .15f);
		Gizmos.DrawLine(DoorExterior.GroundPosition(c_raise), DoorInterior.GroundPosition(c_raise));
		Gizmos.color = Color.cyan;
		Gizmos.DrawSphere(doorCenter, .15f);
		Gizmos.color = Color.magenta;
		Gizmos.DrawSphere(HeightReferencePoint, .15f);
		Gizmos.color = new Color(1, .6f, 0);
		Gizmos.DrawSphere(m_exitPosition.GroundPosition(c_raise), .15f);
		Gizmos.DrawLine(doorCenter, m_exitPosition.GroundPosition(c_raise));

		if (m_queue != null && m_queue.m_path != null)
		{
			Gizmos.color = Color.yellow;
			var rpos = m_queue.GetPathPosition().GroundPosition(c_raise);
			Gizmos.DrawSphere(rpos, .15f);
			for (int i = 0; i < 5; ++i)
			{
				var (pos, fwd) = m_queue.Position(i);
				Gizmos.color = Color.red;
				Gizmos.DrawSphere(pos.GroundPosition(c_raise), .05f);
				Gizmos.DrawSphere(pos.GroundPosition(c_raise) + fwd * .05f, .025f);
			}
		}
	}
#endif
}
