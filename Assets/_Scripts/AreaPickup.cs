using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AreaPickup : DragBase {
	public float m_captureRadiusStartTime = .1f;
	public float m_captureRadiusEndTime = 2f;
	public float m_captureRadiusStartSize = 0;
	public float m_captureRadiusEndSize = 2f;
	float m_captureStartTime;
	public void Check() {
		if (!GameManager.InputConsuming && GameManager.GetMouseButton(0)) {
			if (Input.GetMouseButtonDown(0)) m_captureStartTime = Time.unscaledTime;
			float timeCapturing = Time.unscaledTime - m_captureStartTime;
			float lerp = (timeCapturing - m_captureRadiusStartTime) / (m_captureRadiusEndTime - m_captureRadiusStartTime);
			float captureRadius = Mathf.Lerp(m_captureRadiusStartSize, m_captureRadiusEndSize, lerp);
		} else {
			// end
		}
	}
}
