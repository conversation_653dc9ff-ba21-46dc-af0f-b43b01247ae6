using System;
using TMPro;
using UnityEngine;

public class CursorInfo : MonoBehaviour
{
    [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> m_xzInput = null;
    [SerializeField] private TextMeshProUGUI m_xzWorld = null;
    [SerializeField] private TextMeshProUGUI m_objectInfo = null;
	
    private Vector3 m_mousePosPrevious = Vector3.zero;
    private Vector3 m_camPosPrevious = Vector3.zero;
    private Quaternion m_camRotPrevious = Quaternion.identity;
    private RectTransform m_rectTransform = null;
    
    private void Awake()
    {
	    m_rectTransform = GetComponent<RectTransform>();
	    transform.parent = GameManager.Me.m_fullScreenCanvas;
    }

    private void Update()
    {
	    m_xzInput.text = $"X: {Input.mousePosition.x}, Y: {Input.mousePosition.y}";

	    m_xzWorld.enabled = true;
	    if (HaveTransformsChanged(Input.mousePosition))
	    {
		    Ray heldCharacterRay = Camera.main.ScreenPointToRay(Input.mousePosition);
		    if (Physics.Raycast(heldCharacterRay, out RaycastHit hitInfoTerrain, int.MaxValue,
			        1 << GlobalData.Me.m_moaTerrain.gameObject.layer))
		    {
			    m_xzWorld.text = $"X: {hitInfoTerrain.point.x:F}, Y: {hitInfoTerrain.point.y:f}, Z: {hitInfoTerrain.point.z:F}";
		    }
		    else
		    {
			    m_xzWorld.enabled = false;
		    }
		    
		    LayerMask layerMaskDefault = 1 << LayerMask.NameToLayer("Default");
		    m_objectInfo.enabled = true;
		    if (Physics.Raycast(heldCharacterRay, out RaycastHit hitInfo, int.MaxValue, layerMaskDefault))
		    {
			    m_objectInfo.text = $"{hitInfo.collider.transform.Path()}";
		    }
		    else
		    {
			    m_objectInfo.enabled = false;
		    }//"Here is some text that I want <mark=#ffff0080><font="Helvetica">Highlighted</font></mark> !"
	    }
	    m_rectTransform.position = Input.mousePosition;
    }
    
    private bool HaveTransformsChanged(Vector3 _mousePosNow)
    {
	    Camera mainCam = Camera.main;
	    if (mainCam == null) return false;
	    
	    if (Input.mousePosition.Approximately(m_mousePosPrevious) == false)
	    {
		    m_mousePosPrevious = _mousePosNow;
		    return true;
	    }

	    Transform camTr = mainCam.transform;
	    Vector3 camPos = camTr.position;
	    if (camPos.Approximately(m_camPosPrevious) == false)
	    {
		    m_camPosPrevious = camPos;
		    return true;
	    }
		
	    Quaternion camRot = camTr.rotation;
	    if (camRot.Approximately(m_camRotPrevious, Mathf.Epsilon * 8f) == false)
	    {
		    m_camRotPrevious = camRot;
		    return true;
	    }
	    return false;
    }
}
