using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NGOrderTile : NGDirectionCardBase
{
    protected const string c_borderSpritePath = "MA/_Art/CardBorders/";
    [Header("Order Tile")]
    public Image m_factionIcon = null;
    public Image m_orderBorder = null;
    public Image m_partialDesignProvided = null;
    public bool m_scaleDownWhileDragging = true;
    public Image m_lockBackground = null;
    public RewardElementUI m_rewardPrefab;
    public Transform m_rewardHolder;
    public OrderTileView m_standardView;
    public OrderTileView m_simpleView;
    public OrderTileView m_detailedView;

    private GameObject m_progressBarObject = null;
    private GameObject m_lockRoot = null;
    private MAOrder m_order = MAOrder.EmptyOrder;
    
    public int OrderId => m_order.OrderId;
    
    public GameObject LockRoot
    {
        get
        {
            if(m_lockRoot == null) m_lockRoot = m_lockBackground.gameObject;
            return m_lockRoot;
        }
    }
    
    public MAOrder Order => m_order;
    
    public override bool ScaleCardDownOnDrag => m_scaleDownWhileDragging;
    public override void DragAttemptStarted()
    {
        m_viewOverride = INGDecisionCardHolder.ECardView.None;
    }

    public static string FactionToAudioSwitch(string _faction)
    {
        switch (_faction)
        {
            case "Peoples":
                return "Faction_People";
            case "Lords":
                return "Faction_Lords";
            case "Royal":
                return "Faction_Kings";
            default:
                return "Faction_None";
        }
    }
    
    override public string DragAudioSwitch => FactionToAudioSwitch(m_order.OrderInfo.m_faction);
    override public string DragAudio => "PlaySound_OrderCard_Take";
    override public string DragCancelAudio => "PlaySound_OrderCard_Return";
    override public string DragAssignedAudio => "PlaySound_OrderCard_Release";

    protected override bool TryDropOnTarget(MABuilding _building)
    {
        if(_building == null && Order.IsNullOrEmpty() == false)
            Order.Return();
        
        if(_building == null) return false;
        if(m_order.IsNullOrEmpty()) return false;
        if(m_order.AssignedBuilding == _building) return false; // Make sure we don't apply the order to the same building
        
        return true; 
    }
    
    protected override void Awake()
    {
        base.Awake();
        m_progressBarObject = m_progressBar.gameObject;
        m_lockRoot = m_lockBackground.gameObject;
        m_lockRoot.SetActive(false);
    }

    protected override void Update()
    {
        base.Update();
        UpdateDynamicInfo();
    }

    public void SetupOrderUI(MAOrder _maOrder, float _fractionGained = -1, float _completion = -1)
    {
        if(_maOrder.IsValid == false)
        {
            Debug.LogError($"{GetType().Name} - SetupOrderUI - invalid order provided. '{_maOrder.DisplayName}'. Details below:" +
                           $"\n {_maOrder.DebugOutputString}");
            return;
        }

        if(string.IsNullOrWhiteSpace(_maOrder.OrderInfo.m_orderBoarder) == false && m_orderBorder != null)
        {
            m_orderBorder.sprite = Resources.Load<Sprite>($"{c_borderSpritePath}{_maOrder.OrderInfo.m_orderBoarder}");
        }
        
        if(string.IsNullOrWhiteSpace(_maOrder.OrderInfo.m_faction) == false)
        {
            MAFactionInfo factionInfo = _maOrder.OrderInfo.Faction;
            if(factionInfo == null)
            {
                Debug.LogError($"{GetType().Name} - No FactionInfo for order id {_maOrder.OrderId}. giftTitle: {_maOrder.TemplateBusinessGift.m_giftTitle}. Details below:" +
                               $"\n {_maOrder.DebugOutputString}");
                return;
            }
            
            m_factionIcon.sprite = factionInfo.m_iconSprite;
        }

        m_order = _maOrder;

        UpdatePartialDesign(_maOrder.OrderInfo.m_design);
        
        UpdateDynamicInfo();

        SetupRewardsInfoUI(_maOrder.Rewards);
    }
    
    private void UpdatePartialDesign(string _design)
    {
        if(string.IsNullOrWhiteSpace(_design) == false)
        {
            if(m_order != null && m_order.GameProduct != null)
            {
                m_partialDesignProvided.sprite = GameManager.Me.GetDesignSprite(m_order.GameProduct.Design, CaptureObjectImage.Use.Product,
                (Sprite _sprite) =>
                {
                    if(_sprite != null)
                    {
                        m_partialDesignProvided.sprite = _sprite;
                        m_partialDesignProvided.enabled = true;
                    }
                    else
                    {
                        m_partialDesignProvided.gameObject.SetActive(false);
                        m_partialDesignProvided.enabled = false;
                    }
                });
            }
        }
        else
        { 
            m_partialDesignProvided.gameObject.SetActive(false);  
        }
    }
    
    override public bool TryReleaseOverCustomHolder(INGDecisionCardHolder _holder)
    {
        return false;
    }

    private INGDecisionCardHolder.ECardView m_viewOverride = INGDecisionCardHolder.ECardView.None;
    
    private INGDecisionCardHolder.ECardView GetCurrentView()
    {
        if(m_viewOverride != INGDecisionCardHolder.ECardView.None) return m_viewOverride;
        if(m_fromHolder == null) return INGDecisionCardHolder.ECardView.Standard;
        return m_fromHolder.CardView;
    }
   
    public void SetViewOverride(INGDecisionCardHolder.ECardView _view)
    {
        m_viewOverride = _view;
    }
    
    protected override bool CreateCardInfoGUI()
    {
        if(m_fromHolder == null)
            return false;
            
        if(m_fromHolder.ShowOrderBoardGUIOnCardClick && Order.OwnerBuilding != null)
        {
            MAOrderBoardUI.Create(m_order);
            return true;
        }
        
        var currentView = GetCurrentView();
        if(currentView != INGDecisionCardHolder.ECardView.Simple)
        {
            if(currentView == INGDecisionCardHolder.ECardView.Detailed)
                SetViewOverride(INGDecisionCardHolder.ECardView.Standard);
            else if(currentView == INGDecisionCardHolder.ECardView.Standard)
                SetViewOverride(INGDecisionCardHolder.ECardView.Detailed);
                
            SetCardView(GetCurrentView());
        }
        
        return true;
    }

    public override void GiveReward()
    {
        base.GiveReward();
        MABuilding building = m_destFactory as MABuilding;

        if(building == null)
            return;

        MAOrder maOrder = m_order;

        if(maOrder.IsAvailable)
        {
            BCFactory[] factoryComponent = building.GetComponentsInChildren<BCFactory>(true);
            if(factoryComponent.Length > 0)
            {
                Debug.Assert(maOrder.m_orderQuantity > 0, $"Warning, Order id {maOrder.OrderId} has <= 0 quantity {maOrder.m_orderQuantity}");
                building.ReceiveOrder(maOrder);
            }
        }
    }
    
    public void SetupRewardsInfoUI(MARewardOrderInfo[] _rewards)
    {
        m_rewardHolder.DestroyChildren();
        
        if(_rewards == null || _rewards.Length == 0)
            return;
        
        foreach(var reward in _rewards)
        {
            Sprite icon = null;
            int quantity = 0;
            if(reward.GiftReward != null && string.IsNullOrWhiteSpace(reward.GiftReward.id) == false)
            {
                icon = reward.GiftReward.GetSprite;
                quantity = 1;
            }
            else if(reward.m_currencyRewardValue > 0 && string.IsNullOrWhiteSpace(reward.m_currencyReward) == false)
            {
                NGCarriableResource resourceData = NGCarriableResource.GetInfo(reward.m_currencyReward);
                if(resourceData != null)
                {
                    icon = resourceData.SpriteImage();
                    quantity = Mathf.RoundToInt(reward.m_currencyRewardValue);
                }
            }
            
            if(quantity > 0 && icon != null)
            {
                var rewardElement = Instantiate(m_rewardPrefab, m_rewardHolder);
                rewardElement.SetIcon(icon);
                rewardElement.SetIconQuantity(quantity);
            }
        }
    }

    // for Product Testing room: we display a card which shows incremental gains over time
    string RewardProgress(int _total, float _fractionGained, float _completion)
    {
        if (_fractionGained < 0) return "";
        if (_fractionGained > 0)
        {
            var count = Mathf.RoundToInt(_total * _fractionGained * _completion);
            if (count > 0)
            {
                return $"<color=#00ff00>  +{count}</color>";
            }
            return "";
        }
        return $"<color=#ff0000>  +0</color>";
    }

    /// <summary>
    /// is this a display card sitting on a world display sign, product testing display or design table?
    /// </summary>
    /*public bool IsInfoCard
    {
        get
        {
            if(m_order.AssignedBuilding != null)
            {
                List<BCBase> orderInfoBoards =
                    m_order.AssignedBuilding.ComponentLists<BCOrderInfo>();
                if(orderInfoBoards.Count > 0)
                {
                    foreach(BCBase maBuildingComponentBase in orderInfoBoards)
                    {
                        BCOrderInfo orderInfoComponent = maBuildingComponentBase as BCOrderInfo;
                        if(orderInfoComponent.OrderCardDisplay.OrderInfoCard == this) return true;
                    }
                }

                if(ProductTestingManager.Me.m_orderTile == this) return true;
                if(GetComponentInParent<MAOrderCardDoubleSided>() != null) return true;
            }
            return false;
        }
    }*/

    private void SetCardViewActive(OrderTileView _view, bool _visible)
    {
        if(_view == null) return;
        if(_view.gameObject.activeSelf == _visible) return;
        _view.gameObject.SetActive(_visible);
    }
    
    private void SetCardView(INGDecisionCardHolder.ECardView _view)
    {
        switch(_view)
        {
            case INGDecisionCardHolder.ECardView.Detailed:
                SetCardViewActive(m_detailedView, true);
                SetCardViewActive(m_simpleView, false);
                SetCardViewActive(m_standardView, false);            
                break;
            case INGDecisionCardHolder.ECardView.Simple:
                SetCardViewActive(m_detailedView, false);
                SetCardViewActive(m_simpleView, true);
                SetCardViewActive(m_standardView, false);
                break;
            case INGDecisionCardHolder.ECardView.Standard:
                SetCardViewActive(m_detailedView, false);
                SetCardViewActive(m_simpleView, false);
                SetCardViewActive(m_standardView, true);
                break;
        }
    }
    
    private void UpdateDynamicInfo()
    {
        var cardView = GetCurrentView();
        SetCardView(cardView);

        if(Order.IsValid)
        {
            bool lockCard = m_order.IsAvailable == false && m_fromHolder != null && m_fromHolder.CardView != INGDecisionCardHolder.ECardView.Simple; //&& IsInfoCard == false;
            m_disableInteraction = lockCard;
            LockRoot.SetActive(lockCard);
            var assignedBuilding = m_order.AssignedBuilding;
            if (assignedBuilding != null && Order.HasPlayerDesigned)
            {
                m_progressBarObject.SetActive(true);
                m_progressBar.fillAmount = assignedBuilding.GetProductScore();
            }
            else
            {
                m_progressBarObject.SetActive(false);
            }
        }
    }
    
    public new static NGOrderTile Create(GameObject _prefab, NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder, Transform _holder)
    {
        GameObject go = Instantiate(_prefab, _holder);
        NGOrderTile bd = go.GetComponent<NGOrderTile>();
        bd.Activate(_gift, _maParserSection, _fromHolder);
        return bd;
    }
    
    public new static NGOrderTile Setup(NGOrderTile _tile, NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder, Transform _holder)
    {
        _tile.Activate(_gift, _maParserSection, _fromHolder);
        _tile.transform.SetParent(_holder, false);
        return _tile;
    }
}