using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using TMPro;
using UnityEngine.Rendering.Universal;

public class SettingsUIController : BaseClosableUIController
{
	public Image m_backgroundTint;
	
	public static bool s_saveEnabled = true;

	[SerializeField] TextMeshProUGUI m_quitButtonText;
	
	[SerializeField] Slider m_musicSlider;
	[SerializeField] TextMeshProUGUI m_musicSliderText;
	[SerializeField] Slider m_sfxSlider;
	[SerializeField] TextMeshProUGUI m_SFXSliderText;
	[SerializeField] Slider m_voSlider;
	[SerializeField] TextMeshProUGUI m_VOSliderText;
	[SerializeField] Slider m_moveSpeedSlider;
	[SerializeField] TextMeshProUGUI m_MoveSpeedSliderText;
	[SerializeField] Slider m_rotateSpeedSlider;
	[Serial<PERSON><PERSON>ield] TextMeshProUGUI m_RotateSpeedSliderText;
	[SerializeField] Slider m_zoomSpeedSlider;
	[SerializeField] TextMeshProUGUI m_ZoomSpeedSliderText;
	[SerializeField] Slider m_gammaSlider;
	[SerializeField] TextMeshProUGUI m_gammaSliderText;
	[SerializeField] Slider m_powerSlider;
	[SerializeField] TextMeshProUGUI m_powerSliderText;
	[SerializeField] Slider m_aaSlider;
	[SerializeField] TextMeshProUGUI m_aaSliderText;
	[SerializeField] Slider m_textureQualitySlider;
	[SerializeField] TextMeshProUGUI m_textureQualitySliderText;
	[SerializeField] Slider m_shadowQualitySlider;
	[SerializeField] TextMeshProUGUI m_shadowQualitySliderText;
	[SerializeField] Slider m_envQualitySlider;
	[SerializeField] TextMeshProUGUI m_envQualitySliderText;
	[SerializeField] Slider m_genQualitySlider;
	[SerializeField] TextMeshProUGUI m_genQualitySliderText;
	[SerializeField] Slider m_cameraMovementYSmoothSlider;
	[SerializeField] TextMeshProUGUI m_cameraMovementYSmoothSliderText;
	[SerializeField] Slider m_cameraMovementXZSmoothSlider;
	[SerializeField] TextMeshProUGUI m_cameraMovementXZSmoothSliderText;
	Slider m_physQualitySlider;
	TextMeshProUGUI m_physQualitySliderText;
	Slider m_treeQualitySlider;
	TextMeshProUGUI m_treeQualitySliderText;
	Slider m_dofLevelSlider;
	TextMeshProUGUI m_dofLevelSliderText;
	
	[SerializeField] private Slider m_HUDScaleSlider;
	private float m_HUDScale = .75f;
	[SerializeField] TextMeshProUGUI m_HUDScaleSliderText;
	
	[SerializeField] private Slider m_GUIScaleSlider;
	private float m_GUIScale = 1f;
	[SerializeField] TextMeshProUGUI m_GUIScaleSliderText;

	[SerializeField] Toggle m_hapticToggle;
	[SerializeField] Toggle m_languageToggle;
	[SerializeField] Toggle m_languageKeyToggle;
	[SerializeField] Toggle m_frenchToggle;
	[SerializeField] Toggle m_germanToggle;

	bool m_vsync = true;
	bool m_ssao = true;
	bool m_aniso = true;
	bool m_silenceAll = false;

	[SerializeField] UIButton m_customerSupportButton;
	[SerializeField] UIButton m_clearSaveButton;
	[SerializeField] UIButton m_quitButton;
	[SerializeField] TMP_Dropdown m_qualityDropdown;
	[SerializeField] TMP_Dropdown m_languageDropdown;
	[SerializeField] GameObject m_resolutionDropdownHolder;
	[SerializeField] GameObject m_displaysDropdownHolder;
	[SerializeField] GameObject m_windowModeDropdownHolder;
    [SerializeField] Toggle m_subtitleToggle;
	[SerializeField] Toggle m_vsyncToggle;
	[SerializeField] Toggle m_ssaoToggle;
	[SerializeField] Toggle m_anisoToggle;
	[SerializeField] Toggle m_silenceAllToggle;
	[SerializeField] ScriptableRendererFeature m_SSAOFeature;
    [SerializeField] GameObject m_controlsTab;
    [SerializeField] GameObject m_controlsPanel;
    private PanelToggle[] m_panelToggles;
	
	const int c_minSliderValue = 0;
	const int c_maxSliderValue = 100;
	static bool s_subtitlesActive = true;
	private bool m_isInitialised = false;
	private float c_minHUDScaleValue = .25f;
	private float c_maxHUDScaleValue = 2f;
	private float c_minGUIScaleValue = .25f;
	private float c_maxGUIScaleValue = 2f;

	private GameObject m_audioMasteringDropdownHolder;


    public static bool Subtitles { get { return s_subtitlesActive;  } }

	public static SettingsUIController Open(Action _onCustomerSupportClicked, Action<int> _onMusicDragged, Action<int> _onSFXDragged, Action _onClearSaveClicked = null)
	{
		AudioClipManager.Me.PlayUISound("PlaySound_TitleScreenSettings");
		SettingsUIController uiController = InfoPlaqueManager.Me.LoadUI<SettingsUIController>();
		uiController.Setup(_onCustomerSupportClicked, _onClearSaveClicked);
		uiController.Show();

		try
		{
			if (!string.IsNullOrEmpty(GameManager.UserId))
				GUIUtility.systemCopyBuffer = $"Player ID: {GameManager.UserId}";
		}
		catch
		{
		}

		return uiController;
	}

	private static void SetSlider(Slider _slider, int _min, int _max, int _current)
	{
		_slider.minValue = _min;
		_slider.maxValue = _max;
		_slider.value = _current;

	}
	private void SetSlider(Slider _slider, float _min, float _max, float _current)
	{
		_slider.minValue = _min;
		_slider.maxValue = _max;
		_slider.value = _current;

	}

	public void ShowDesignerNotesDialogue()
	{
		GameManager.Me.CreateDesignNotesDialogue(null);
	}

	private static DebugConsole.Command s_deletePrefsCmd = new ("deleteprefs", _s => {
		MPlayerPrefs.DeleteAll();
		MPlayerPrefs.Save();
		Debug.LogError("Deleted PlayerPrefs");
	});
	
	public override void OnCloseComplete()
	{
		PauseManager.Resume();
	}
	
	private static int s_lastOpenPanel = 0;
	public void Setup(Action _onCustomerSupportClicked, Action _onClearSaveClicked = null)
	{
		if (_onClearSaveClicked == null) PauseManager.Pause();

		if (m_backgroundTint != null)
			m_backgroundTint.color = new Color(0, 0, 0, 0);

		m_quitButtonText.text = "Quit";//GameManager.Me.IsInTitleScreen() ? "Quit" : "Quit To Titles";
		
		m_panelToggles = GetComponentsInChildren<PanelToggle>();
		for (int i = 0; i < m_panelToggles.Length; ++i) m_panelToggles[i].Control.isOn = false;
		m_panelToggles[s_lastOpenPanel].Control.isOn = true;
		for (int i = 0; i < m_panelToggles.Length; ++i) m_panelToggles[i].Update();
		
		m_customerSupportButton.onButtonClick += (o) => {
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			_onCustomerSupportClicked();
			Close(); };
#if true//UNITY_EDITOR || DEVELOPMENT_BUILD
		if(_onClearSaveClicked != null)
			m_clearSaveButton.onButtonClick += (o) => {
				HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
				_onClearSaveClicked();
				Close(false); };
		else
#endif
			m_clearSaveButton.gameObject.SetActive(false);
		if (true || _onClearSaveClicked != null)
			m_quitButton.onButtonClick += (o) => {
				HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
				Close(false);
				GameManager.CloseSessionAndQuit(); };
		else
			m_quitButton.onButtonClick += (o) => {
				HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
				Close(false);
				GameManager.ExitToTitleScreen(true); };
		
		s_subtitlesActive = MPlayerPrefs.GetInt("Subtitles") != 0;

		MakeSlider(out m_physQualitySlider, out m_physQualitySliderText, m_genQualitySlider, m_genQualitySliderText, 0, 2, 1, "Physics Quality");
		MakeSlider(out m_treeQualitySlider, out m_treeQualitySliderText, m_genQualitySlider, m_genQualitySliderText, 0, 3, 3, "Tree Quality");
		MakeSlider(out m_dofLevelSlider, out m_dofLevelSliderText, m_genQualitySlider, m_genQualitySliderText, 0, 3, 3, "Depth-of-field");

		MakeDropdown(out m_audioMasteringDropdownHolder, m_displaysDropdownHolder, m_musicSlider.transform.parent.parent, "Listening Mode");
		FillAudioMasteringDropdown();

		InitSliderUpdates();
		
		m_vsync = GameSettings.VSync() != 0;
		m_ssao = GameSettings.SSAO() != 0;
		m_aniso = GameSettings.AnisotropicFiltering() != 0;
		m_HUDScale = GameSettings.HUDScale();
		m_GUIScale = GameSettings.GUIScale();
		m_silenceAll = AudioClipManager.Me.GetSilenceAll();
		GameSettings.m_SSAOFeature = m_SSAOFeature;
		SetSlider(m_HUDScaleSlider, c_minHUDScaleValue, c_maxHUDScaleValue, m_HUDScale);
		SetSlider(m_GUIScaleSlider, c_minGUIScaleValue, c_maxGUIScaleValue, m_GUIScale);

		m_subtitleToggle.isOn = s_subtitlesActive;
		m_vsyncToggle.isOn = m_vsync;
		m_vsyncToggle.onValueChanged.AddListener((b) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			m_vsync = b;
			GameSettings.VSync(b ? 1 : 0);
		});
		m_ssaoToggle.isOn = m_SSAOFeature.isActive;
		m_ssaoToggle.onValueChanged.AddListener((b) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			m_ssao = b;
			GameSettings.SSAO(b ? 1 : 0);
		});
		m_anisoToggle.isOn = m_aniso;
		m_anisoToggle.onValueChanged.AddListener((b) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			m_aniso = b;
			GameSettings.AnisotropicFiltering(b ? 1 : 0);
		});
		m_silenceAllToggle.isOn = m_silenceAll;
		m_silenceAllToggle.onValueChanged.AddListener((b) =>
		{
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			m_silenceAll = b;
			AudioClipManager.Me.SetSilenceAll(b);
		});

		InitialiseDisplayIndex();
		InitialiseHardwareDropDown();
		InitialiseResolutionDropDown();
		InitialiseWindowDropDown();
        m_currentLanguage = MPlayerPrefs.GetString("Language", "English");
        InitialiseLanguageDropDown();

        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_Window_Open", transform);

		UpdateText();

#if UNITY_IOS || UNITY_ANDROID
		m_GUIScaleSlider.transform.parent.gameObject.SetActive(false);
		m_HUDScaleSlider.transform.parent.gameObject.SetActive(false);
#else
		m_powerSlider.transform.parent.gameObject.SetActive(false);
#endif
		
        //#if UNITY_IOS || UNITY_ANDROID

        int hfb = MPlayerPrefs.GetInt("HapticFB", 1);

		m_hapticToggle.isOn = (hfb == 1);
		HapticInterface.IsEnabled = m_hapticToggle.isOn;
        m_hapticToggle.onValueChanged.AddListener((b) =>
        {
            HapticInterface.IsEnabled = b;
			HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
			MPlayerPrefs.SetInt("HapticFB", b ? 1 : 0);
        });
        //#endif

#if UNITY_EDITOR || DEVELOPMENT_BUILD
        LocalizeKnack.m_translateLanguageKey = false;
		m_languageKeyToggle.isOn = LocalizeKnack.m_translateLanguageKey;
		m_languageKeyToggle.onValueChanged.AddListener((b) =>
		{
            LocalizeKnack.m_translateLanguageKey = m_languageKeyToggle.isOn;
		});
#endif

		m_isInitialised = true;
	}
	
	private static void MakeSlider(out Slider _slider, out TextMeshProUGUI _text, Slider _copy, TextMeshProUGUI _copyText, int _min, int _max, int _current, string _name)
	{
		var pattern = _copy.transform.parent.gameObject;
		var copy = Instantiate(pattern, pattern.transform.parent);
		var label = copy.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
		_slider = copy.transform.GetChild(1).GetComponent<Slider>();
		_slider.minValue = _min;
		_slider.maxValue = _max;
		_slider.value = _current;
		label.text = _name;
		_text = _slider.GetComponentInChildren<TextMeshProUGUI>();
	}

	private static void MakeDropdown(out GameObject _dropdown, GameObject _template, Transform _parent, string _label)
	{
		_dropdown = Instantiate(_template, _parent);
		var labelObj = _dropdown.GetComponentInChildren<TextMeshProUGUI>();
		labelObj.text = _label;
	}

	private int SpeedToLinear(float _speed) // 0 - 50 - 100 <=> .5 - 1 - 2
	{
		_speed *= 100; // 50 - 100 - 200
		if (_speed < 100) return (int)_speed - 50;
		return (int)_speed / 2;
	}

	private float LinearToSpeed(int _linear)
	{
		float f = (float)_linear * .01f; // 0 - .5 - 1
		if (f < .5f) return f + .5f;
		return f * 2;
	}

	private int SmoothToLinear(float _smooth)
	{
		_smooth -= 0.8f;
		_smooth /= 0.4f;
		_smooth *= 100f;
		return (int)_smooth;
	}

	private float LinearToSmooth(int _linear)
	{
		float f = (float)_linear * 0.01f;
		return (f * 0.4f) + 0.8f;
	}
	
	private void FillAudioMasteringDropdown()
	{
		m_audioMasteringDropdownHolder.SetActive(true);
		var dropdown = m_audioMasteringDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		var values = new List<TMP_Dropdown.OptionData>();
		for (int i = 0; i < AudioClipManager.s_audioMasteringTypes.Length; ++i)
		{
			values.Add(new TMP_Dropdown.OptionData(AudioClipManager.s_audioMasteringTypes[i]));
		}
		dropdown.options = values;
		dropdown.value = AudioClipManager.Me.GetAudioMastering();
		dropdown.onValueChanged.AddListener(_i => { AudioClipManager.Me.SetAudioMastering(_i); });

	}

	static List<DisplayInfo> s_displayLayout = new List<DisplayInfo>();

	private static List<DisplayInfo> GetDisplays()
	{
		s_displayLayout.Clear();
		Screen.GetDisplayLayout(s_displayLayout);
		return s_displayLayout;
	}
	public static int CurrentDisplayIndex()
	{
		return GetDisplays().IndexOf(Screen.mainWindowDisplayInfo);
	}

    private int m_currentDisplayIndex = -1; 
	private void CheckDisplayIndex()
	{
#if UNITY_IOS || UNITY_ANDROID
#else
		if (m_currentDisplayIndex != CurrentDisplayIndex())
		{
			if (s_saveEnabled)
				MPlayerPrefs.SetInt(c_displayPlayerPrefsKey, CurrentDisplayIndex());
			InitialiseResolutionDropDown();
		}
#endif
	}

	private void InitialiseDisplayIndex()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_displaysDropdownHolder.SetActive(false);
#else
		m_currentDisplayIndex = MPlayerPrefs.GetInt(c_displayPlayerPrefsKey, 0);
		if (m_currentDisplayIndex != CurrentDisplayIndex()) SetDisplay(m_currentDisplayIndex);
#endif
	}
	
	const string c_resPlayerPrefsKey = "SRes";
	const string c_displayPlayerPrefsKey = "DisplayName";
	static Resolution s_currentResolution;
	static int s_currentResolutionWindowed => (MPlayerPrefs.GetInt(c_resPlayerPrefsKey) >>28) &3;
	static void SetResolution(Resolution _res, int _windowed)
	{
		s_currentResolution = _res;
		Utility.DoNextFrame(() =>
		{
			var windowMode = GetPrefWindowMode();
			Screen.SetResolution(s_currentResolution.width, s_currentResolution.height, windowMode);
			if (s_saveEnabled)
				MPlayerPrefs.SetInt(c_resPlayerPrefsKey, s_currentResolution.width + (s_currentResolution.height << 14) + (_windowed << 28));

			Debug.Log($"Set Res {s_currentResolution.width} x {s_currentResolution.height} win {s_currentResolutionWindowed}");
			for (int i = 0; i < Display.displays.Length; ++i)
				Debug.Log($"Display {i} - {Display.displays[i].systemWidth}x{Display.displays[i].systemHeight} ({Display.displays[i].renderingWidth}x{Display.displays[i].renderingHeight})");
		});
	}
	static void SetResolution(int _w, int _h, int _windowed)
	{
		int bestIndex = 0, bestD2 = 0x7FFFFFFF, bestXD2 = 0x7FFFFFFF;
		for (int i = 0; i < Screen.resolutions.Length; ++i)
		{
			var r = Screen.resolutions[i];
			if (r.width == _w && r.height == _h)
			{
				SetResolution(r, _windowed);
				return;
			}
			var dy = r.height - _h;
			var dx = r.width - _w;
			if (dy * dy < bestD2 || (dy * dy == bestD2 && dx * dx < bestXD2))
			{
				bestD2 = dy * dy;
				bestXD2 = dx * dx;
				bestIndex = i;
			}
		}
		SetResolution(Screen.resolutions[bestIndex], _windowed);
	}

	public static void ChangeResolution(int _w, int _h)
	{
		var windowMode = GetPrefWindowMode();
		Screen.SetResolution(_w, _h, windowMode);
	}

	private static int s_originalWindowW, s_originalWindowH, s_originalWindowId;
	private static bool s_originalWindowActive = true;
	public static void ToggleWindow()
	{
		s_originalWindowActive = !s_originalWindowActive;
		if (s_originalWindowActive)
		{
			LoadResolution(s_originalWindowW, s_originalWindowH, s_originalWindowId, false);
		}
		else
		{
			LoadResolution(1280, 1024, 0, false);
		}
	}
	
	public static void LoadResolution(int _overrideW, int _overrideH, int _overrideId = -1, bool _storeDefaults = true)
	{
		if (_overrideW != -1)
		{
			Screen.fullScreen = false;
			Utility.DoNextFrame(() =>
			{
				var display = GetDisplays()[0];
				int displayW = display.width;
				if (_overrideW < 0) _overrideW = displayW / -_overrideW; // -n means fit n windows across
				
				Screen.SetResolution(_overrideW, _overrideH, FullScreenMode.Windowed);
				if (_overrideId != -1)
				{
					int cols = displayW / _overrideW;
					int x = _overrideId % cols, y = _overrideId / cols;
					Debug.LogError($"Window {_overrideId} {_overrideW}x{_overrideH} disp:{display.width}x{display.height} c:{cols} x:{x} y:{y}");
					const int c_titleBarHeight = 24;
					var wpos = new Vector2Int(x * _overrideW, c_titleBarHeight + y * (_overrideH + c_titleBarHeight));
					Screen.MoveMainWindowTo(display, wpos);
					if (_storeDefaults)
					{
						s_originalWindowW = _overrideW;
						s_originalWindowH = _overrideH;
						s_originalWindowId = _overrideId;
					}
				}
			});
			return;
		}
#if UNITY_IOS || UNITY_ANDROID
		return;
#endif
		var packed = MPlayerPrefs.GetInt(c_resPlayerPrefsKey, -1);
		if (packed == -1) return;

		var w = packed & ((1 << 14) - 1);
		var h = (packed >> 14) & ((1 << 14) - 1);
		var fs = (packed >> 28) & 3;
		if (w == 0 || h == 0) {
			w = 1920;
			h = 1080;
		}
		SetResolution(w, h, fs);
	}

	private void SetDisplay(int _index)
	{
		if (_index < 0 || _index >= GetDisplays().Count) _index = 0;
		var async = Screen.MoveMainWindowTo(GetDisplays()[_index], Screen.mainWindowPosition);
		async.completed += _s =>
		{
			if (s_saveEnabled)
				MPlayerPrefs.SetInt(c_displayPlayerPrefsKey, _index);
			InitialiseResolutionDropDown();
			InitialiseWindowDropDown();
		};
	}



	void InitialiseWindowDropDown()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_windowModeDropdownHolder.gameObject.SetActive(false);
#else
        var packed = MPlayerPrefs.GetInt(c_resPlayerPrefsKey, -1);

        m_windowModeDropdownHolder.SetActive(true);
		var window = m_windowModeDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		//add options
		var options = new List<TMP_Dropdown.OptionData>();
		options.Add(new TMP_Dropdown.OptionData("Window Borderless"));
		options.Add(new TMP_Dropdown.OptionData("FullScreen"));
		options.Add(new TMP_Dropdown.OptionData("Windowed"));
		window.options = options;

		window.value = (MPlayerPrefs.GetInt(c_resPlayerPrefsKey) >> 28) & 3;

        window.onValueChanged.AddListener(_w => { SetWindowMode(window.value); });
#endif
    }
    private void SetWindowMode(int _index)
    {
		if (s_saveEnabled)
			MPlayerPrefs.SetInt(c_resPlayerPrefsKey, s_currentResolution.width + (s_currentResolution.height << 14) + (_index << 28));
		Screen.fullScreenMode = GetPrefWindowMode();
        InitialiseWindowDropDown();
        InitialiseResolutionDropDown();
        CheckResolution();
    }

	private static FullScreenMode GetPrefWindowMode()
	{
		var _window = (MPlayerPrefs.GetInt(c_resPlayerPrefsKey) >> 28) & 3;
        switch (_window)
        {
            case 0:
                return FullScreenMode.MaximizedWindow;
            case 1:
                return FullScreenMode.ExclusiveFullScreen;
            case 2:
                return FullScreenMode.Windowed;
            default:
                return FullScreenMode.ExclusiveFullScreen;
        }
    }

    void InitialiseResolutionDropDown()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_windowModeDropdownHolder.SetActive(false);
		m_vsyncToggle.gameObject.SetActive(false);
		m_resolutionDropdownHolder.SetActive(false);
#else
		m_currentDisplayIndex = CurrentDisplayIndex();
		
		m_resolutionDropdownHolder.SetActive(true);
		var dropdown = m_resolutionDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		//var toggle = m_windowedToggle;//m_resolutionDropdownHolder.GetComponentInChildren<Toggle>();

		m_displaysDropdownHolder.SetActive(true);
		var displaysDropdown = m_displaysDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		var displays = new List<TMP_Dropdown.OptionData>();
		var activeDisplays = GetDisplays();
		int selDisplay = -1;
		for (int i = 0; i < activeDisplays.Count; ++i)
		{
			displays.Add(new TMP_Dropdown.OptionData(activeDisplays[i].name));
			if (i == m_currentDisplayIndex) selDisplay = i;
		}
		displaysDropdown.options = displays;
		displaysDropdown.value = selDisplay;
		displaysDropdown.onValueChanged.AddListener(_d =>
		{
			SetDisplay(_d);
		});

		var options = new List<TMP_Dropdown.OptionData>();
		int sel = -1;
		int bestSelD2 = 0x7FFFFFFF;
		int currW = Screen.width, currH = Screen.height;
		var resolutions = Screen.resolutions;
		if (GetPrefWindowMode() == FullScreenMode.Windowed)
		{
			var display = activeDisplays[m_currentDisplayIndex];
			int maxWidth = display.width, maxHeight = display.height;
			
			var winRes = new List<Resolution>();
			for (int i = 0; i < m_windowedResolutions.Length; ++i)
			{
				var r = m_windowedResolutions[i];
				if (r.width <= maxWidth && r.height <= maxHeight)
					winRes.Add(r);
			}
			if (winRes[winRes.Count - 1].width != maxWidth || winRes[winRes.Count - 1].height != maxHeight)
				winRes.Add(new Resolution { width = maxWidth, height = maxHeight, refreshRate = 0 });
			resolutions = winRes.ToArray();
		}
		foreach (var r in resolutions)
		{
			var dr = r.refreshRate > 0 ? $"{r.width} x {r.height} @ {r.refreshRate}Hz" : $"{r.width} x {r.height}";
			options.Add(new TMP_Dropdown.OptionData(dr));
			int w = r.width, h = r.height;
			int dw = w - currW, dh = h - currH;
			var d2 = dw * dw + dh * dh;
			if (d2 < bestSelD2)
			{
				bestSelD2 = d2;
				sel = options.Count - 1;
			}
		}
		dropdown.options = options;
		dropdown.value = sel;
		//toggle.isOn = s_currentResolutionWindowed;	
		dropdown.onValueChanged.AddListener((r) => SetResolution(resolutions[r], s_currentResolutionWindowed));
		//toggle.onValueChanged.AddListener((b) => SetResolution(s_currentResolution, b));
		m_currentSelectedResolution = resolutions[sel];
#endif
	}
    Resolution[] m_windowedResolutions =
	{
		new () { width = 1280, height = 720, refreshRate = 0 },
		new () { width = 1366, height = 768, refreshRate = 0 },
		new () { width = 1600, height = 900, refreshRate = 0 },
		new () { width = 1920, height = 1080, refreshRate = 0 },
		new () { width = 2560, height = 1440, refreshRate = 0 },
	};
    Resolution m_currentSelectedResolution;

    void CheckResolution()
    {
	    int w = Screen.width, h = Screen.height;
	    if (w != m_currentSelectedResolution.width || h != m_currentSelectedResolution.height)
		    SetResolution(m_currentSelectedResolution, s_currentResolutionWindowed);
    }

    public void OnToggleChanged(bool _v)
    {
        m_subtitleToggle.isOn = _v;
        s_subtitlesActive = _v;
        int v = s_subtitlesActive ? 1 : 0;
        if (s_saveEnabled)
			MPlayerPrefs.SetInt("Subtitles", v);
    }
	
	private static string[] s_languages = {"English"};
	private string m_currentLanguage = "English";

	public void OnShowControlsClicked()
	{
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		if (GameManager.Me.IsOKToPlayUISound())
		{
			AudioClipManager.Me.PlaySoundOld("PlaySound_GUIInfoWindowOpen", transform);
		}
	}

	public void OnShowControlsCloseClicked()
	{
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		if (GameManager.Me.IsOKToPlayUISound())
		{
			AudioClipManager.Me.PlaySoundOld("PlaySound_GUIInfoWindowClose", transform);
		}
	}

	void SetLanguage(int _lang)
	{
		string language = s_languages[0];
		if (_lang > 0)
		{
			language = NGLanguages.s_languages[_lang - 1].m_name;
		}

		m_currentLanguage = language;
        // save/restore, change language
        MPlayerPrefs.SetString("Language", language);
		LocalizeKnack.m_language = language;
		LocalizeKnack.ReloadNGTranslated(language);
        GameManager.Me.RefreshStartButton();
        InitialiseLanguageDropDown();
		InitialiseHardwareDropDown();
        UpdateText();
    }

    void InitialiseLanguageDropDown()
	{
        var options = new List<TMP_Dropdown.OptionData>();
		int sel = -1;
		foreach (var l in s_languages)
		{
			options.Add(new TMP_Dropdown.OptionData(LocalizeKnack.TranslateLocalisedString(l)));
			if (l == m_currentLanguage) sel = options.Count - 1;
		}

		foreach (var l in NGLanguages.s_languages)
		{
			options.Add(new TMP_Dropdown.OptionData(LocalizeKnack.TranslateLocalisedString(l.m_name)));
			if (l.m_name == m_currentLanguage) sel = options.Count - 1;
		}
		m_languageDropdown.options = options;
		m_languageDropdown.value = sel;
		m_languageDropdown.onValueChanged.AddListener((l) => SetLanguage(l));
	}

	public enum EQualityLevels
	{
		Low, Medium, High,
		//
		Last,
	}
	private EQualityLevels m_currentQuality = EQualityLevels.High;

	public void SetQualityLevel(EQualityLevels _quality)
	{
		m_currentQuality = _quality;
		// TODO - load/save, implement quality level
	}
	void InitialiseHardwareDropDown()
	{
#if false//UNITY_IOS && !UNITY_EDITOR
		m_qualityDropdown.transform.parent.gameObject.SetActive(false);
#else
		var options = new List<TMP_Dropdown.OptionData>();
		for (int i = 0; i < (int)EQualityLevels.Last; i++)
		{
			string q = ((EQualityLevels)i).ToString();
			options.Add(new TMP_Dropdown.OptionData(LocalizeKnack.TranslateLocalisedString(q)));
		}
		m_qualityDropdown.options = options;
		m_qualityDropdown.value = (int)m_currentQuality;
		m_qualityDropdown.onValueChanged.AddListener(l => SetQualityLevel((EQualityLevels)l));
#endif
	}
	
	private static bool CheckSlider(Slider _slider, ref int _value)
	{
		if ((int)_slider.value == _value) return false;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.MoveSlider);
		_value = (int)_slider.value;
		return true;
	}
	
	private bool CheckSlider(Slider _slider, ref float _value)
	{
		if (_slider.value == _value) return false;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.MoveSlider);
		_value = _slider.value;
		return true;
	}

	private bool CheckSliderAdjusted(Slider _slider, ref float _value)
	{
		float v = (int)(_slider.value * 4f) / 4f;
		if (v == _value) return false;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.MoveSlider);
		_value = _slider.value;
		return true;
	}

	public class SliderUpdate
	{
		public static List<SliderUpdate> s_allSliders = new();
		public static void Clear() => s_allSliders.Clear();
		
		public static void UpdateAll()
		{
			foreach (var s in s_allSliders) s.Update();
		}

		public static void SetLabels()
		{
			foreach (var s in s_allSliders) s.Update(true);
		}
		

		public Slider m_slider;
		public TextMeshProUGUI m_sliderLabel;
		public int m_value;
		public Func<int, string> m_setter;

		public SliderUpdate(Slider _slider, TextMeshProUGUI _label, Func<int, string> _setter, int _initial)
		{
			m_slider = _slider;
			m_sliderLabel = _label;
			m_setter = _setter;
			m_value = _initial;
			s_allSliders.Add(this);
			SetSlider(m_slider, c_minSliderValue, c_maxSliderValue, m_value);
			Update(true);
		}
		
		public void Update(bool _forceUpdateLabel = false)
		{
			if (_forceUpdateLabel || CheckSlider(m_slider, ref m_value))
			{
				m_sliderLabel.text = m_setter(m_value);
			}
		}
	}

	private void InitSliderUpdates()
	{
		SliderUpdate.Clear();
		
		// == audio ==
		new SliderUpdate(m_musicSlider, m_musicSliderText,
			_v =>
			{
				AudioClipManager.Me.SetMusicVolume(_v * 0.01f);
				return $"{_v}";
			},
			Mathf.RoundToInt(AudioClipManager.Me.GetMusicVolume() * 100f));
		new SliderUpdate(m_sfxSlider, m_SFXSliderText,
			_v =>
			{
				AudioClipManager.Me.SetSFXVolume(_v * 0.01f);
				return $"{_v}";
			},
			Mathf.RoundToInt(AudioClipManager.Me.GetSFXVolume() * 100f));
		new SliderUpdate(m_voSlider, m_VOSliderText,
			_v =>
			{
				AudioClipManager.Me.SetVOVolume(_v * 0.01f);
				return $"{_v}";
			},
			Mathf.RoundToInt(AudioClipManager.Me.GetVOVolume() * 100f));

		// == control speed ==
		new SliderUpdate(m_moveSpeedSlider, m_MoveSpeedSliderText,
			_v =>
			{
				GameManager.Me.CameraMoveSpeed(LinearToSpeed(_v));
				return SpeedToLabel(_v);
			},
			SpeedToLinear(GameManager.Me.CameraMoveSpeed()));
		new SliderUpdate(m_rotateSpeedSlider, m_RotateSpeedSliderText,
			_v =>
			{
				GameManager.Me.CameraRotateSpeed(LinearToSpeed(_v));
				return SpeedToLabel(_v);
			},
			SpeedToLinear(GameManager.Me.CameraRotateSpeed()));
		new SliderUpdate(m_zoomSpeedSlider, m_ZoomSpeedSliderText,
			_v =>
			{
				GameManager.Me.CameraZoomSpeed(LinearToSpeed(_v));
				return SpeedToLabel(_v);
			},
			SpeedToLinear(GameManager.Me.CameraZoomSpeed()));
		new SliderUpdate(m_cameraMovementYSmoothSlider, m_cameraMovementYSmoothSliderText,
			_v =>
			{
				float smooth = LinearToSmooth(_v);
				GameManager.Me.CameraMovementYSmoothFactor(smooth);
				return $"{smooth}";
			},
			SmoothToLinear(GameManager.Me.CameraMovementYSmoothFactor()));
		new SliderUpdate(m_cameraMovementXZSmoothSlider, m_cameraMovementXZSmoothSliderText,
			_v =>
			{
				float smooth = LinearToSmooth(_v);
				GameManager.Me.CameraMovementXZSmoothFactor(smooth);
				return $"{smooth}";
			},
			SmoothToLinear(GameManager.Me.CameraMovementXZSmoothFactor()));
		
		// == visuals ==
		new SliderUpdate(m_gammaSlider, m_gammaSliderText,
			_v =>
			{
				GameSettings.Gamma(LinearToSpeed(_v));
				return $"{_v}";
			},
			SpeedToLinear(GameSettings.Gamma()));
		new SliderUpdate(m_powerSlider, m_powerSliderText,
			_v =>
			{
				GameSettings.Power(_v);
				return $"{_v}";
			},
			SpeedToLinear(GameSettings.Power()));
		new SliderUpdate(m_aaSlider, m_aaSliderText,
			_v =>
			{
				GameSettings.AntiAliasing(_v / 25);
				return LocalizeKnack.TranslateLocalisedString(s_antiAliasLabels[_v / 25]);
			},
			GameSettings.AntiAliasing() * 25 + 12);
		new SliderUpdate(m_textureQualitySlider, m_textureQualitySliderText,
			_v =>
			{
				GameSettings.TextureResolution(_v / 25);
				return LocalizeKnack.TranslateLocalisedString(s_qualityLabels[_v / 25]);
			},
			GameSettings.TextureResolution() * 25 + 12);
		new SliderUpdate(m_shadowQualitySlider, m_shadowQualitySliderText,
			_v =>
			{
				GameSettings.ShadowResolution(_v / 25);
				return LocalizeKnack.TranslateLocalisedString(s_qualityLabels[_v / 25]);
			},
			GameSettings.ShadowResolution() * 25 + 12);
		new SliderUpdate(m_envQualitySlider, m_envQualitySliderText,
			_v =>
			{
				GameSettings.Environment(_v / 33);
				return LocalizeKnack.TranslateLocalisedString(s_quality3Labels[_v / 33]);
			},
			GameSettings.Environment() * 33 + 16);
		new SliderUpdate(m_genQualitySlider, m_genQualitySliderText,
			_v =>
			{
				GameSettings.Quality(_v / 50);
				return LocalizeKnack.TranslateLocalisedString(s_quality3Labels[(_v / 51) * 2]);
			},
			GameSettings.Quality() * 50 + 25);
		new SliderUpdate(m_physQualitySlider, m_physQualitySliderText,
			_v =>
			{
				GameSettings.Physics(_v / 33);
				return LocalizeKnack.TranslateLocalisedString(s_quality3Labels[_v / 33]);
			},
			GameSettings.Physics() * 33 + 16);
		new SliderUpdate(m_treeQualitySlider, m_treeQualitySliderText,
			_v =>
			{
				GameSettings.TreeQuality(_v / 25);
				return LocalizeKnack.TranslateLocalisedString(s_qualityLabels[_v / 25]);
			},
			GameSettings.TreeQuality() * 25 + 12);
		new SliderUpdate(m_dofLevelSlider, m_dofLevelSliderText,
			_v =>
			{
				GameSettings.DOFLevel(_v / 25);
				return LocalizeKnack.TranslateLocalisedString(s_qualityPlusOffLabels[_v / 25]);
			},
			GameSettings.DOFLevel() * 25 + 12);
	}
	
	protected override void Update()
	{
		CheckDisplayIndex();
		
		SliderUpdate.UpdateAll();
		
		if (CheckSliderAdjusted(m_HUDScaleSlider, ref m_HUDScale))
		{
			m_HUDScale = (int)(m_HUDScale * 4f) / 4f;
			GameSettings.HUDScale(m_HUDScale);
			m_HUDScaleSliderText.text = $"{(m_HUDScale*100):F0}%";
		}

		if (CheckSliderAdjusted(m_GUIScaleSlider, ref m_GUIScale))
		{
			m_GUIScale = (int)(m_GUIScale * 4f) / 4f;
			GameSettings.GUIScale(m_GUIScale);
			m_GUIScaleSliderText.text = $"{(m_GUIScale*100):F0}%";
		}

		for (int i = 0; i < m_panelToggles.Length; ++i) if (m_panelToggles[i].Control.isOn) s_lastOpenPanel = i;
		if (m_backgroundTint != null)
		{
			float targetBackgroundAlpha = s_lastOpenPanel == 1 ? 0 : .75f;
			float alpha = Mathf.Lerp(m_backgroundTint.color.a, targetBackgroundAlpha, .2f);
			m_backgroundTint.color = new Color(0, 0, 0, alpha);
		}
	}

	private static string[] s_antiAliasLabels = { "None", "Low", "Mid", "High", "High" };
	private static string[] s_quality3Labels = { "Low", "Mid", "High", "High" };
	private static string[] s_qualityLabels = { "Low", "Mid", "High", "Full", "Full" };
	private static string[] s_qualityPlusOffLabels = { "Off", "Low", "Mid", "High", "Full", "Full" };
	
	private string SpeedToLabel(int _value)
	{
		if (_value < 33) return "Slow";
		if (_value > 66) return "Fast";
		return "Mid";
	}
	private void UpdateText()
	{
		SliderUpdate.SetLabels();
		m_HUDScaleSliderText.text = $"{(m_HUDScale*100):F0}%";
		m_GUIScaleSliderText.text = $"{(m_GUIScale*100):F0}%";
	}

	public void PlayToggleSound(bool _on)
    {
		if (m_isInitialised && GameManager.Me.IsOKToPlayUISound())
		{
			if (_on)
			{
				AudioClipManager.Me.PlaySoundOld("PlaySound_Checkbox_Tick", GameManager.Me.transform);
			}
			else
            {
				AudioClipManager.Me.PlaySoundOld("PlaySound_Checkbox_Untick", GameManager.Me.transform);
			}
		}
	}
}