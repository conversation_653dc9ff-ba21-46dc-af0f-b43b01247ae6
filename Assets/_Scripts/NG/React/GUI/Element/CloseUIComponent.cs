using UnityEngine.EventSystems;

public class CloseUIComponent : BaseNoGraphic, IUIExtensionComponent, IPointerDownHandler {

	private BaseUIController uiController;

	public void OnPointerDown(PointerEventData eventData) {
		raycastTarget = false;

		//destroy both raycast results, because we want to ignore the default UI raycast behaviour
		RaycastResult currentRaycast = eventData.pointerCurrentRaycast;
		currentRaycast.Clear();
		eventData.pointerCurrentRaycast = currentRaycast;

		RaycastResult pressRaycast = eventData.pointerPressRaycast;
		pressRaycast.Clear();
		eventData.pointerPressRaycast = pressRaycast;

		uiController.ClosedFromBackground();
		uiController.Close();
	}

	public void Setup(BaseUIController uiController) {
		this.uiController = uiController;
	}

}