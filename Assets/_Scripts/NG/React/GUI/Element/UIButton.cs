using System;
using UnityEngine.EventSystems;

public class UIButton : UIComponentWithId<Button_v0> {

	public event Action<PointerEventData> onButtonClick {
		add {
			if (component == null)
				GetComponent<UnityEngine.UI.Button>().onClick.AddListener(() => value(null));
			else
				component.onButtonClick += value;
		}
		remove {
			if (component == null)
				;
			else
				component.onButtonClick -= value;
		}
	}

	public bool interactable {
		get {
			return component.interactable;
		}
		set {
			component.interactable = value;
		}
	}

	public bool isOn {
		set => gameObject.SetActive(value);
	}

	public UnityEngine.UI.Selectable.Transition transition {
		get {
			return component.transition;
		}
		set {
			component.transition = value;
		}
	}

	public void ClearOnButtonClick() {
		component.ClearOnButtonClick();
	}

}