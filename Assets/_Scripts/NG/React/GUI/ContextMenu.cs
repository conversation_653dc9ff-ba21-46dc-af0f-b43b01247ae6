using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ContextMenu : MonoBehaviour
{
	[SerializeField] TextMeshProUGUI m_reactNameText;
	[SerializeField] ContextMenuButton m_buttonPrefab;
	[SerializeField] ContextMenuButton m_goldButtonPrefab;
	[SerializeField] ContextMenuButton m_upgradeButtonPrefab;
	[SerializeField] List<RectTransform> m_Anchor6OptionsList;
	[SerializeField] List<RectTransform> m_Anchor5OptionsList;
	[SerializeField] List<RectTransform> m_Anchor4OptionsList;
	[SerializeField] List<RectTransform> m_Anchor3OptionsList;
	[SerializeField] List<RectTransform> m_Anchor2OptionsList;
	[SerializeField] List<RectTransform> m_Anchor1OptionsList;
	[SerializeField] List<RectTransform> m_AnchorTitleList;

	public float m_dragDistance = 0.008f;
	public object m_owner = null;

	List<ContextMenuButton> m_buttons;
	public List<ContextMenuButton> Buttons {get {return m_buttons; } }
	public Transform m_tutorialGoldButtonTransformRef = null;
	public static bool DisplayGoldPrices {get; set;} = true;
	public static int s_maxButtons = 5;
	 
	private BezierLine m_line;
	private float m_lineAlpha = 0;

	const bool c_clampToScreen = true;
	const bool c_contextAtDoor = true;
	const bool c_contextBezier = false;

	//---------------------------------------------------------------------------------------------------------
	public void Initialise(ContextMenuData _data, object _owner)
	{
		HapticInterface.PlayUIHaptic(true);
		
		var offClick = GetComponentInChildren<OffClickEvent>();
		if(offClick != null)
			offClick.OnClicked = OnOffClickEvent;

		m_owner = _owner;
		int numButtons = _data.m_buttonDataList.Count;

		transform.position = Input.mousePosition;
		m_reactNameText.text = LocaliseContextNames(_data);
		m_buttons = new List<ContextMenuButton>();

		// Use the correct list of anchors
		List<RectTransform> anchors = m_Anchor5OptionsList;

		switch(numButtons)
		{
			case 1: anchors = m_Anchor1OptionsList;
				break;
			case 2: anchors = m_Anchor2OptionsList;
				break;
			case 3: anchors = m_Anchor3OptionsList;
				break;
			case 4: anchors = m_Anchor4OptionsList;
				break;
			case 5: anchors = m_Anchor5OptionsList;
				break;
			default: anchors = m_Anchor6OptionsList;
				break;
		}
		
		SetupButtons(anchors, _data.m_buttonDataList);

		// Position the title
		m_reactNameText.transform.parent.transform.position = m_AnchorTitleList[0].transform.position;

		var line = Instantiate(GlobalData.Me.m_pickupLinePrefab);
		line.SetLayerRecursively(0);
		m_line = line.GetComponent<BezierLine>();
		
		Animate(true);
	}

	void Animate(bool _show) {
		GetComponent<Animator>().SetBool("is_open", _show);
	}
	
    private void SetPosition()
	{
		float lineAlpha = 0;

		var ngBase = m_owner as NGCommanderBase;
		var canvas = GameManager.Me.m_mainGameUI.GetComponentInParent<Canvas>();
		if(ngBase != null)
		{
			var anchorPosWorld = c_contextAtDoor ? ngBase.DoorPosInner.GroundPosition(4) : ngBase.GetHighestPoint();
			var pos = canvas.WorldToCanvas(anchorPosWorld, Camera.main);

			float canvasH = 1080, canvasW = canvasH * Screen.width / Screen.height;
			if (c_clampToScreen)
			{
				float borderX = canvasH * .3f;
				float borderY = canvasH * .16f;
				float clampX = canvasW * .5f - borderX;
				float clampY = canvasH * .5f - borderY;
				var rawPos = pos;
				pos.x = Mathf.Clamp(pos.x, -clampX, clampX);
				pos.y = Mathf.Clamp(pos.y, -clampY, clampY);
				if (c_contextBezier && (rawPos - pos).sqrMagnitude > canvasH * .05f * canvasH * .05f)
					lineAlpha = 1;
			}

			var screenPos = new Vector3(
				(pos.x + canvasW * .5f) * Screen.width / canvasW,
				(pos.y + canvasH * .5f) * Screen.height / canvasH,
				20);
			m_line.SetControlPoints(anchorPosWorld, Camera.main.ScreenToWorldPoint(screenPos));

			transform.localPosition = pos;
		}
		else
		{
			var b = m_owner as NGLegacyBase;
			if(b != null)
			{
				var pos = canvas.WorldToCanvas(b.transform.position, Camera.main);
				transform.localPosition = pos;
			}
		}
		m_lineAlpha = Mathf.Lerp(m_lineAlpha, lineAlpha, .1f);
		float smoothAlpha = m_lineAlpha * m_lineAlpha * (3 - m_lineAlpha - m_lineAlpha);
		var lineRenderer = m_line.GetComponent<LineRenderer>();
		DragBase.SetBezierColour(lineRenderer, new Color(.5f, .5f, .4f, smoothAlpha));
		DragBase.SetBezierXrayColour(lineRenderer, new Color(.3f, .3f, .4f, smoothAlpha));
		bool enableLine = m_lineAlpha > 0.001f;
		if (m_line.gameObject.activeSelf != enableLine)
			m_line.gameObject.SetActive(enableLine);
	}

	//because we've built the context menu from object name strings, passing the data to this function to localise. Currently handles edgecases until localisation plans are correct. 
	private string LocaliseContextNames(ContextMenuData _data){
		if(_data.m_title == "House Worker"){
			return "Worker House";
		}
		return _data.m_title;
	}

	float m_toolTipDelay = 1f;
    public void Update()
    {
        SetPosition();
        if(m_toolTipDelay > 0)
        {
	        m_toolTipDelay -= Time.deltaTime;
	        
	        if(m_toolTipDelay <= 0)
	        {
				SetToolTipsEnabled(true);
	        }
        }
	}

    //---------------------------------------------------------------------------------------------------------
    public ContextMenuButton GetButton (int _index) {
		return m_buttons[Mathf.Min (_index, m_buttons.Count)];
	} 
	//---------------------------------------------------------------------------------------------------------
	public void OnDestroy()
	{
		Destroy(m_line.gameObject);
		m_line = null;
		m_owner = null;
	}

	//---------------------------------------------------------------------------------------------------------
	void HideMenu(bool _offClick = false)
	{
		HapticInterface.PlayUIHaptic(false);
		Animate(false);
		ContextMenuManager.Me.RemoveCurrentMenu(_offClick);
	}

    //---------------------------------------------------------------------------------------------------------
    public void OnOffClickEvent()
	{
		HideMenu(true);
	}

	//---------------------------------------------------------------------------------------------------------
	public void HandleFlickGesture(float _distance)
	{
		if (_distance >= m_dragDistance)
		{
			Vector3 buttonToMouse;
			float distanceToButton;
			float smallestDistance = 1000000.0f;
			int buttonIndex	= 0;		

			// Find the closest button to our current mouse position
			for(int i = 0; i < m_buttons.Count; i++)
			{
				buttonToMouse = m_buttons[i].transform.position - Input.mousePosition;
				distanceToButton = buttonToMouse.sqrMagnitude;

				if (distanceToButton < smallestDistance)
				{
					smallestDistance = distanceToButton;
					buttonIndex = i;
				}
			}

			m_buttons[buttonIndex].m_button.onClick.Invoke();
		}
	}
	
	public void SetToolTipsEnabled(bool _enabled)
	{
		foreach(var button in m_buttons)
		{
			// Inhibit tool tip to prevent accidently firing on creation
			var handler = button.GetComponentInChildren<TooltipHandler>();
			if (handler != null) handler.enabled = _enabled;
		}
	}

	//---------------------------------------------------------------------------------------------------------
	void SetupButtons(List<RectTransform> _anchors, List<ContextMenuData.ButtonData> _buttonDatas)
	{
		ContextMenuButton button;
		ContextMenuData.EDirection arrowDirection;

		for (int i = 0; i < _buttonDatas.Count; i++)
		{
			if (_buttonDatas[i] != null)
			{
				// Set the arrow directions
				switch (i)
				{
					default:
					case 0: arrowDirection = (_buttonDatas.Count == 1 ? ContextMenuData.EDirection.Down : ContextMenuData.EDirection.Right);
						break;
					case 1: arrowDirection= (_buttonDatas.Count == 2 ? ContextMenuData.EDirection.Left : ContextMenuData.EDirection.Down);
						break;
					case 2: arrowDirection = ContextMenuData.EDirection.Left;
						break;
					case 3: arrowDirection = (_buttonDatas.Count == 4 ? ContextMenuData.EDirection.Up : ContextMenuData.EDirection.LeftUp);
						break;
					case 4: arrowDirection = ContextMenuData.EDirection.RightUp;
						break;
					case 5: arrowDirection = ContextMenuData.EDirection.Up;
						break;
				}
				if(_buttonDatas[i].m_goldCostLabel != null) {
					button = Instantiate(m_goldButtonPrefab, transform, false);
					m_tutorialGoldButtonTransformRef = button.transform;
				} else if(_buttonDatas[i].m_label == Localizer.Get(TERM.GUI_UPGRADE)){
					button = Instantiate(m_upgradeButtonPrefab, transform, false);
				} else {
					button = Instantiate(m_buttonPrefab, transform, false);
				}
					
				button.SetPosition(_anchors[i].anchoredPosition, arrowDirection);

				// Set up text and display gold if not tutorial
				if(DisplayGoldPrices && _buttonDatas[i].m_goldCostLabel != null)
					button.SetText($"{_buttonDatas[i].m_label}: {_buttonDatas[i].m_goldCostLabel}");
				else
				{
					if(_buttonDatas[i].m_goldCostLabel != null)
					{
						var goldIcon = button.transform.FindChildRecursiveByName("icon");
						if(goldIcon != null) goldIcon.gameObject.SetActive(false);
					}
					button.SetText(_buttonDatas[i].m_label);
				}

				button.SetAudioHook(_buttonDatas[i].m_audioHook);
				button.SetButtonType(_buttonDatas[i].m_buttonType);
				button.OnClickCallback = _buttonDatas[i].m_onClick;
				button.SetButtonInteractableCondition(_buttonDatas[i].m_interactableCallback);
				button.SetButtonInteractable(_buttonDatas[i].m_interactable);
				m_buttons.Add(button);
			}
		}
		SetToolTipsEnabled(false);
	}


}
