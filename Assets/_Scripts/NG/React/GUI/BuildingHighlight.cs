using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class BuildingHighlight : MonoBehaviour
{
	public NGCommanderBase Building { get { return m_building; } }
	
    public TMP_Text m_nameText;
    private NGCommanderBase m_building;

	private void Activate(NGCommanderBase _b)
	{
		transform.localPosition = new Vector3(0, -2.8f, 0);
		m_nameText.text = _b.m_title;
        m_building = _b;
        _b.HighlightBuilding();
	}

	public void DestroyMe()
	{
		m_building.UnhighlightBuilding();
		Destroy(gameObject);
	}

	public static BuildingHighlight Create(NGCommanderBase _building)
	{
		if (_building == null) return null;
		var go = Instantiate(GameManager.Me.m_buildingHighlightPrefab, _building.m_balloonHolder, _building);
		var hl = go.GetComponent<BuildingHighlight>();
		hl.Activate(_building);
		return hl;
	}
}
