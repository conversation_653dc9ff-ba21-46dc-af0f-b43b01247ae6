using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UITimeController : MonoBehaviour
{
	private static readonly int HASH_MINUTES = Shader.PropertyToID("_Minutes");
	private static readonly int HASH_HOURS = Shader.PropertyToID("_Hours");
	private static readonly int HASH_DAYS = Shader.PropertyToID("_Days");
	private MaterialPropertyBlock m_blockTime;
	[SerializeField]private MeshRenderer m_renderer;
	private int m_maxMins = 5;
	private int m_maxHours = 0;
	private int m_maxDays = 0;

    // Start is called before the first frame update
	public void SetMaxValues (int _mins, int _days = 0, int _hours = 0){
		m_maxMins = _mins;
		m_maxHours = _hours;
		m_maxDays = _days;
	}
    public void SetTime(int _days, int _hours, int _mins)
    {
		if(m_blockTime == null)
			m_blockTime = new MaterialPropertyBlock();

		_days = Mathf.Min(_days, m_maxDays);
		_hours = Mathf.Min(_hours, m_maxHours);
		_mins = Mathf.Min(_mins, m_maxMins);// max 5 mins
		m_blockTime.SetFloat(HASH_DAYS, _days);
		m_blockTime.SetFloat(HASH_HOURS, _hours);
		m_blockTime.SetFloat(HASH_MINUTES, _mins);
		
		m_renderer.SetPropertyBlock(m_blockTime);
    }

}
